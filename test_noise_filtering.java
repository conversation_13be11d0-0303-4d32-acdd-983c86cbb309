// 簡單的噪音過濾測試示例
// 這個文件用於演示新的噪音過濾功能

public class NoiseFilteringDemo {
    
    public static void main(String[] args) {
        // 模擬你提供的OCR輸出
        String noisyOCROutput = 
            "方式 瀏覽記錄 書籤 設定檔 分頁 視窗 輔助說明\n" +
            "N\n" +
            "繁饼\n" +
            "干杯~-b x 哗哩哔哩()口干杯~-bix] 哔哩哔哩 (ˊ)口千杯~bx] 哔哩哔哩 (↑\n" +
            "哔哩哔哩(↑↑00千杯--bㄨ\n" +
            "9 但願來生不相逢線上看第01集\n" +
            "G/33 B 950-9-1. html\n" +
            "首頁\n" +
            "電視劇\n" +
            "動漫\n" +
            "電影\n" +
            "綜藝\n" +
            "排行 成人\n" +
            "1 B 禁\n" +
            "輸入影片關鍵字. . . . Q\n" +
            "白玉珠好友\n" +
            "王\n" +
            "建\n" +
            "国\n" +
            "好看 那就不贵\n" +
            "7月\n" +
            "2 G\n" +
            "M\n" +
            "N\n" +
            "DFl\n" +
            "F 2\n" +
            "0\n" +
            "F 3\n" +
            "2\n" +
            "3\n" +
            "$\n" +
            "4\n" +
            "F 4\n" +
            "%5\n" +
            "G\n" +
            "の>\n" +
            "DII\n" +
            "元\n" +
            "AFG\n" +
            "F 7\n" +
            "FB. F 9\n" +
            "F 10\n" +
            "F 11\n" +
            "&\n" +
            "7\n" +
            "W\n" +
            "E RTY U\n" +
            "B\n" +
            "9\n" +
            "0\n" +
            "0\n" +
            "P\n" +
            "+\n" +
            "F 12";

        System.out.println("=== 原始OCR輸出 ===");
        System.out.println("總行數: " + noisyOCROutput.split("\n").length);
        System.out.println("總字符數: " + noisyOCROutput.length());
        System.out.println("\n內容預覽:");
        System.out.println(noisyOCROutput.substring(0, Math.min(200, noisyOCROutput.length())) + "...");

        // 預期的過濾結果（基於我們的過濾規則）
        String expectedFilteredOutput = 
            "方式 瀏覽記錄 書籤 設定檔 分頁 視窗 輔助說明\n" +
            "繁饼\n" +
            "但願來生不相逢線上看第01集\n" +
            "首頁\n" +
            "電視劇\n" +
            "動漫\n" +
            "電影\n" +
            "綜藝\n" +
            "排行 成人\n" +
            "輸入影片關鍵字\n" +
            "白玉珠好友\n" +
            "好看 那就不贵\n" +
            "7月";

        System.out.println("\n=== 預期過濾結果 ===");
        System.out.println("保留行數: " + expectedFilteredOutput.split("\n").length);
        System.out.println("保留字符數: " + expectedFilteredOutput.length());
        System.out.println("過濾比例: " + String.format("%.1f%%", 
            (1.0 - (double)expectedFilteredOutput.split("\n").length / noisyOCROutput.split("\n").length) * 100));

        System.out.println("\n過濾後內容:");
        System.out.println(expectedFilteredOutput);

        System.out.println("\n=== 過濾規則說明 ===");
        System.out.println("✅ 保留的內容:");
        System.out.println("  - 有意義的中文句子");
        System.out.println("  - 導航和分類項目");
        System.out.println("  - 功能描述文本");
        System.out.println("  - 正常的詞語組合");
        
        System.out.println("\n❌ 過濾的內容:");
        System.out.println("  - 單個字符行 (N, M, G, 王, 建, 国, 等)");
        System.out.println("  - 鍵盤序列 (E RTY U)");
        System.out.println("  - HTML片段 (G/33 B 950-9-1. html)");
        System.out.println("  - 功能鍵序列 (F 2, F 3, F 10, 等)");
        System.out.println("  - 特殊符號組合");
        System.out.println("  - 無意義的字符組合");

        System.out.println("\n=== 實際使用 ===");
        System.out.println("在 GoogleOCR.java 中，這個過濾會在 correctOCRErrors() 方法的第一步執行，");
        System.out.println("確保後續的字符替換和詞邊界修正在更乾淨的文本上進行。");
    }
}
