/**
 * OCR錯誤修正功能演示
 * 展示新實現的OCR錯誤修正功能的效果
 */
public class OCRErrorCorrectionDemo {
    
    public static void main(String[] args) {
        System.out.println("=== OCR錯誤修正功能演示 ===\n");
        
        // 演示各種OCR錯誤類型的修正
        demonstrateCharacterSubstitution();
        demonstrateWordBoundaryErrors();
        demonstratePunctuationErrors();
        demonstrateComplexErrors();
    }
    
    private static void demonstrateCharacterSubstitution() {
        System.out.println("1. 字符替換錯誤修正:");
        
        String[] testCases = {
            "He11o W0rld",           // 數字字母混淆
            "The quick brovvn fox",  // 字母間混淆 (vv -> w)
            "I need he1p",           // 1 vs l
            "G00d m0rning",          // 0 vs O
            "這是一個囗試"            // 中文字符混淆
        };
        
        String[] expected = {
            "Hello World",
            "The quick brown fox", 
            "I need help",
            "Good morning",
            "這是一個口試"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.printf("  輸入: %s\n", testCases[i]);
            System.out.printf("  預期: %s\n", expected[i]);
            System.out.println();
        }
    }
    
    private static void demonstrateWordBoundaryErrors() {
        System.out.println("2. 單詞邊界錯誤修正:");
        
        String[] testCases = {
            "Hel lo wor ld",         // 錯誤分割
            "w ord",                 // 短分割
            "HelloWorld",            // 連接單詞
            "Test123Case",           // 數字字母連接
            "這是test測試case",       // 中英文連接
            "t he quick br own"      // 多個分割錯誤
        };
        
        String[] expected = {
            "Hello world",
            "word",
            "Hello World",
            "Test 123 Case", 
            "這是 test 測試 case",
            "the quick brown"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.printf("  輸入: %s\n", testCases[i]);
            System.out.printf("  預期: %s\n", expected[i]);
            System.out.println();
        }
    }
    
    private static void demonstratePunctuationErrors() {
        System.out.println("3. 標點符號錯誤修正:");
        
        String[] testCases = {
            "Hello,world.How are you?",  // 缺失空格
            "He said ''Hello world''",   // 引號錯誤
            "This is ( a test )",        // 括號空格
            "What ?Really !",            // 問號感嘆號前空格
            "你好，世界。這是測試；"      // 中文標點
        };
        
        String[] expected = {
            "Hello, world. How are you?",
            "He said \"Hello world\"",
            "This is (a test)",
            "What? Really!",
            "你好，世界。這是測試；"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.printf("  輸入: %s\n", testCases[i]);
            System.out.printf("  預期: %s\n", expected[i]);
            System.out.println();
        }
    }
    
    private static void demonstrateComplexErrors() {
        System.out.println("4. 複合錯誤修正:");
        
        String[] testCases = {
            "He11o W0r1d,h0w are y0u?This is a t est.",
            "The qu1ck br0vvn f0x jumps 0ver the 1azy d0g.",
            "Wel c0me t0 0ur c0mpany.We pr0vide the best serv1ce.",
            "這是一個t est案例，包含中英文m1xed內容。"
        };
        
        String[] expected = {
            "Hello World, how are you? This is a test.",
            "The quick brown fox jumps over the lazy dog.",
            "Welcome to our company. We provide the best service.",
            "這是一個 test 案例，包含中英文 mixed 內容。"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.printf("  輸入: %s\n", testCases[i]);
            System.out.printf("  預期: %s\n", expected[i]);
            System.out.println();
        }
    }
}
