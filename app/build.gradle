plugins {
    id 'com.android.application'
    id 'kotlin-android'
}

android {
    namespace 'com.xy.demo'
    packagingOptions {
        jniLibs {
            excludes += ['lib/x86_64/**', 'lib/x86/**']
        }
        resources {
            // 排除所有函式庫中重複的 META-INF 檔案
            excludes += ['META-INF/LICENSE.txt', 'META-INF/NOTICE.txt']
            // 新增：排除導致衝突的重複檔案
            excludes += ['META-INF/DEPENDENCIES']
        }
    }
    signingConfigs {
        config {
            keyAlias 'zmart'
            keyPassword '123456'
            storeFile file('../zmart.jks')
            storePassword '123456'
        }
    }
    compileSdk 36
    defaultConfig {
        applicationId "com.xy.demo"
        minSdkVersion 26
        targetSdkVersion 33
        versionCode 1
        versionName '1.0.0'

        multiDexEnabled true

        ndk{
            abiFilters 'armeabi-v7a','arm64-v8a'
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config
        }
        debug {
            signingConfig signingConfigs.config
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:1.7.1"
    implementation "androidx.core:core-ktx:1.16.0"
    implementation "com.google.android.material:material:1.12.0"
    implementation "androidx.constraintlayout:constraintlayout:2.2.1"

//    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:${kotlinCoroutines}"
//    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
//    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
//    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
//    implementation "com.github.CymChad:BaseRecyclerViewAdapterHelper:$quick_version"
//    implementation "com.afollestad.material-dialogs:core:$dialog_version"
//    implementation "com.tencent.bugly:crashreport:$bugly_version"
//    implementation "com.tencent.bugly:nativecrashreport:$bugly_native_version"
//    api 'com.gyf.immersionbar:immersionbar:3.0.0'
//    implementation "com.github.bumptech.glide:glide:4.10.0"
//    implementation "com.github.bumptech.glide:okhttp3-integration:4.10.0"
//    implementation "com.zlc.glide:webpdecoder:1.6.4.9.0"
//    implementation 'com.tencent:mmkv:1.2.12'

    implementation 'com.google.mlkit:text-recognition:16.0.1'
    implementation 'com.google.mlkit:text-recognition-chinese:16.0.1'

    // Speech SDK
    implementation 'com.microsoft.cognitiveservices.speech:client-sdk:1.45.0'
    implementation files('libs/libausbc.aar')
    implementation files('libs/libnative.aar')
    implementation files('libs/libuvc.aar')
//    implementation files('libs/usb.aar')
    implementation 'com.google.oauth-client:google-oauth-client-jetty:1.39.0'
    implementation 'com.google.api-client:google-api-client-android:2.8.0'
    // https://mvnrepository.com/artifact/com.google.apis/google-api-services-vision
    implementation 'com.google.apis:google-api-services-vision:v1-rev451-1.25.0'
    implementation 'com.google.code.gson:gson:2.13.1'
    def camerax_version = "1.4.2" // 使用一個穩定且廣泛支援的版本
    implementation "androidx.camera:camera-core:${camerax_version}"
    implementation "androidx.camera:camera-camera2:${camerax_version}" // 解決問題的核心：加入 camera2 實現
    implementation "androidx.camera:camera-lifecycle:${camerax_version}"
    implementation "androidx.camera:camera-view:${camerax_version}"

}


//plugins {
//    id 'com.android.application'
////    id 'org.jetbrains.kotlin.android'
////    id 'com.huawei.agconnect'
//}
//
//android {
//    packagingOptions {
//        exclude 'META-INF/DEPENDENCIES'
//    }
//    signingConfigs{
//        config {
//            keyAlias 'zmart'
//            keyPassword '123456'
//            storeFile file('../zmart.jks')
//            storePassword '123456'
//        }
//    }
//    compileSdkVersion 28
//    // compileSdk 32
//    defaultConfig {
//        applicationId "com.xy.demo"
//        minSdkVersion 23
//        targetSdkVersion 27
//        // minSdk 21
//        // targetSdk 32
//        versionCode 1
//        versionName "1.01"
//        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
//        ndk {
//            abiFilters 'armeabi-v7a', 'arm64-v8a'
//        }
//        //abiFilters 'armeabi-v7a', 'arm64-v8a'
//    }
//    lintOptions {
//        checkReleaseBuilds false
//        // Or, if you prefer, you can continue to check for errors in release builds,
//        // but continue the build even when errors are found:
//        abortOnError false
//    }
//
//    buildTypes {
//        release {
//            minifyEnabled false
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//            signingConfig signingConfigs.config
//        }
//        debug {
//            signingConfig signingConfigs.config
//        }
//    }
//    defaultConfig {
//        multiDexEnabled true
//    }
//    packagingOptions {
//        doNotStrip "*/armeabi/*.so"
//        doNotStrip "*/armeabi-v7a/*.so"
//        doNotStrip "*/arm64-v8a/*.so"
//        doNotStrip "*/x86/*.so"
//        doNotStrip "*/x86_64/*.so"
//        doNotStrip "*/mips/*.so"
//        doNotStrip "*/mips64/*.so"
//    }
//    // solving com.android.tools.r8.CompilationFailedException: Compilation failed to complete
//    compileOptions {
//        sourceCompatibility JavaVersion.VERSION_1_8
//        targetCompatibility JavaVersion.VERSION_1_8
//    }
//}
//
//dependencies {
//    implementation fileTree(dir: 'libs', include: ['*.jar'])
//    implementation "androidx.appcompat:appcompat:1.3.1"
//    implementation 'com.google.android.material:material:1.2.0'
//    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
////    implementation 'com.huaweicloud.sdk:huaweicloud-sdk-core:3.1.8'
////    implementation 'com.huaweicloud.sdk:huaweicloud-sdk-ocr:3.1.5'
//    implementation 'com.google.oauth-client:google-oauth-client-jetty:1.34.1'
//    implementation 'com.google.api-client:google-api-client-android:2.2.0'
//    // https://mvnrepository.com/artifact/com.google.apis/google-api-services-vision
//    implementation 'com.google.apis:google-api-services-vision:v1-rev451-1.25.0'
//    implementation 'com.google.code.gson:gson:2.8.6'
//    implementation files('libs/libausbc.aar')
//    implementation files('libs/libnative.aar')
//    implementation files('libs/libuvc.aar')
//}
