package com.xy.demo.tts;

import android.media.AudioTrack;
import android.os.Build;
import android.util.Log;

import com.microsoft.cognitiveservices.speech.*;

import java.util.concurrent.atomic.AtomicBoolean;

public class SynthesisRunnable implements Runnable {

    private final String ssml;                 // 改用 SSML
    private final AudioTrack audioTrack;
    private final SpeechSynthesizer synthesizer;
    private final Object syncLock;
    private final AtomicBoolean stopState;
    private final AtomicBoolean isPaused;
    private final float speed;                 // 播放速度 (0.5–2.0)

    public SynthesisRunnable(SpeechSynthesizer synthesizer,
                             AudioTrack audioTrack,
                             Object syncLock,
                             AtomicBoolean stopState,
                             AtomicBoolean isPaused,
                             String ssml,
                             float speed) {
        this.synthesizer  = synthesizer;
        this.audioTrack   = audioTrack;
        this.syncLock     = syncLock;
        this.stopState    = stopState;
        this.isPaused     = isPaused;
        this.ssml         = ssml;
        this.speed        = speed;
    }

    @Override
    public void run() {
        try {
            /* 1. 依需求調整播放速度（API 23+ 支援 setPlaybackParams） */
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                audioTrack.setPlaybackParams(
                        audioTrack.getPlaybackParams().setSpeed(speed));
            }
            audioTrack.play();

            synchronized (syncLock) {
                stopState.set(false);
            }

            /* 2. 邊合成邊播放 —— 根據內容類型選擇正確的API方法 */
            // 🔧 重要修復: 根據內容類型選擇正確的API方法
            // Azure Speech SDK最佳實踐:
            // - StartSpeakingSsmlAsync() 用於SSML內容 (會解析XML結構並只朗讀文本)
            // - StartSpeakingTextAsync() 用於純文本 (會將所有內容當作字面文本朗讀)
            SpeechSynthesisResult result;
            
            if (ssml.trim().startsWith("<?xml") || ssml.trim().startsWith("<speak")) {
                // SSML文檔：使用專用的SSML API
                Log.d("SynthesisRunnable", "Processing as SSML content with StartSpeakingSsmlAsync");
                result = synthesizer.StartSpeakingSsmlAsync(ssml).get();
            } else {
                // 純文本：使用文本API  
                Log.d("SynthesisRunnable", "Processing as plain text with StartSpeakingTextAsync");
                result = synthesizer.StartSpeakingTextAsync(ssml).get();
            }

            /* 3. 將 SpeechSynthesisResult 轉為 AudioDataStream 逐區塊讀取 */
            AudioDataStream stream = AudioDataStream.fromResult(result);

            /* 官方建議：24 kHz × 16 bit × 0.05 s ≈ 2400 bytes */
            byte[] buffer = new byte[2400];

            while (!stopState.get()) {

                synchronized (syncLock) {
                    /* 若外部要求暫停，進入 wait 狀態；resume 時由外層呼叫 syncLock.notifyAll() */
                    while (isPaused.get()) {
                        audioTrack.pause();      // 暫停輸出
                        syncLock.wait();
                        audioTrack.play();       // 恢復輸出
                    }
                }

                long len = stream.readData(buffer);  // 阻塞直到有資料或結束
                if (len == 0) {                      // 0 = 合成完畢
                    Log.i("TTS", "audio complete");
                    stopPlayback();
                    break;
                }
                audioTrack.write(buffer, 0, (int) len);
            }

            stream.close();
        } catch (Exception ex) {
            Log.e("TTS", "unexpected " + ex.getMessage(), ex);
            stopPlayback();
        }
    }

    private void stopPlayback() {
        try {
            audioTrack.stop();
            audioTrack.flush();
        } catch (IllegalStateException ignore) { /* 已停止 */ }
        synchronized (syncLock) {
            stopState.set(true);
        }
    }
}