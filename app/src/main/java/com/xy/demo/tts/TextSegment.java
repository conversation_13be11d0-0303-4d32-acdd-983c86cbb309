package com.xy.demo.tts;

/**
 * 文本片段類 - 用於多語言SSML處理
 * 
 * 根據Unicode屬性將混合語言文本分割為不同的語言片段
 * 每個片段包含文本內容和對應的語言類型
 */
public class TextSegment {
    
    /**
     * 語言類型枚舉
     */
    public enum Language {
        CHINESE("zh"),     // 中文（漢字）
        ENGLISH("en"),     // 英文（拉丁字符）
        MIXED("mixed"),    // 混合（包含多種語言）
        PUNCTUATION("punct"), // 標點符號
        NUMBER("num"),     // 數字
        WHITESPACE("ws"),  // 空白字符
        WEBLINK("web"),    // Web鏈接（http://, https://, www.等）
        BREAK_MARKER("break"); // Break標記（##BREAK_XXX##）
        
        private final String code;
        
        Language(String code) {
            this.code = code;
        }
        
        public String getCode() {
            return code;
        }
        
        /**
         * 判斷是否為主要語言（需要特定語音處理）
         */
        public boolean isPrimaryLanguage() {
            return this == CHINESE || this == ENGLISH;
        }
        
        /**
         * 判斷是否為文本內容（非符號）
         */
        public boolean isTextContent() {
            return this == CHINESE || this == ENGLISH || this == MIXED || this == NUMBER || this == WEBLINK;
        }
    }
    
    private final String text;
    private final Language language;
    private final int startIndex;  // 在原文本中的起始位置
    private final int endIndex;    // 在原文本中的結束位置
    
    /**
     * 構造文本片段
     * @param text 文本內容
     * @param language 語言類型
     */
    public TextSegment(String text, Language language) {
        this(text, language, 0, text != null ? text.length() : 0);
    }
    
    /**
     * 構造文本片段（包含位置信息）
     * @param text 文本內容
     * @param language 語言類型
     * @param startIndex 起始位置
     * @param endIndex 結束位置
     */
    public TextSegment(String text, Language language, int startIndex, int endIndex) {
        this.text = text != null ? text : "";
        this.language = language != null ? language : Language.MIXED;
        this.startIndex = startIndex;
        this.endIndex = endIndex;
    }
    
    /**
     * 獲取文本內容
     */
    public String getText() {
        return text;
    }
    
    /**
     * 獲取語言類型
     */
    public Language getLanguage() {
        return language;
    }
    
    /**
     * 獲取起始位置
     */
    public int getStartIndex() {
        return startIndex;
    }
    
    /**
     * 獲取結束位置
     */
    public int getEndIndex() {
        return endIndex;
    }
    
    /**
     * 獲取文本長度
     */
    public int getLength() {
        return text.length();
    }
    
    /**
     * 判斷是否為空片段
     */
    public boolean isEmpty() {
        return text.isEmpty();
    }
    
    /**
     * 判斷是否為純中文片段
     */
    public boolean isPureChinese() {
        return language == Language.CHINESE;
    }
    
    /**
     * 判斷是否為純英文片段
     */
    public boolean isPureEnglish() {
        return language == Language.ENGLISH;
    }
    
    /**
     * 判斷是否為需要特定語音處理的文本
     */
    public boolean requiresVoiceProcessing() {
        return language.isPrimaryLanguage() && !isEmpty();
    }
    
    /**
     * 創建修剪後的文本片段（去除首尾空白）
     */
    public TextSegment trim() {
        String trimmed = text.trim();
        if (trimmed.equals(text)) {
            return this; // 沒有變化，返回原對象
        }
        return new TextSegment(trimmed, language, startIndex, endIndex);
    }
    
    /**
     * 合併相鄰的相同語言片段
     */
    public TextSegment merge(TextSegment other) {
        if (other == null || this.language != other.language) {
            throw new IllegalArgumentException("Cannot merge segments with different languages");
        }
        
        String mergedText = this.text + other.text;
        int mergedStart = Math.min(this.startIndex, other.startIndex);
        int mergedEnd = Math.max(this.endIndex, other.endIndex);
        
        return new TextSegment(mergedText, this.language, mergedStart, mergedEnd);
    }
    
    @Override
    public String toString() {
        return String.format("TextSegment{text='%s', language=%s, start=%d, end=%d}", 
            text, language, startIndex, endIndex);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        TextSegment segment = (TextSegment) obj;
        return startIndex == segment.startIndex &&
               endIndex == segment.endIndex &&
               text.equals(segment.text) &&
               language == segment.language;
    }
    
    @Override
    public int hashCode() {
        int result = text.hashCode();
        result = 31 * result + language.hashCode();
        result = 31 * result + startIndex;
        result = 31 * result + endIndex;
        return result;
    }
}