package com.xy.demo.tts;

import android.annotation.SuppressLint;
import android.speech.tts.TextToSpeech;
import android.widget.Toast;
import android.content.Context;

import com.xy.demo.R;
import com.xy.demo.utils.PreferenceManager;

import java.util.Locale;

public class EmbedTTS implements  TextToSpeech.OnInitListener{
    private final Context context;
    private final TextToSpeech tts;
    private PreferenceManager preferenceManager;
    public EmbedTTS(Context context, PreferenceManager PreferenceManager) {
        this.context = context;
        this.tts = new TextToSpeech(context, this);
        this.preferenceManager = PreferenceManager;
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onInit(int i) {
        if (i == TextToSpeech.SUCCESS) {
            setTTSLanguage();
        } else if (i == TextToSpeech.ERROR) {
            Toast.makeText(context, context.getString(R.string.tts_error_initializing), Toast.LENGTH_SHORT).show();
        } else if (i == TextToSpeech.ERROR_INVALID_REQUEST) {
            Toast.makeText(context, context.getString(R.string.tts_error_invalid_request), Toast.LENGTH_SHORT).show();
        } else if (i == TextToSpeech.ERROR_NETWORK) {
            Toast.makeText(context, context.getString(R.string.tts_error_network), Toast.LENGTH_SHORT).show();
        } else if (i == TextToSpeech.ERROR_NETWORK_TIMEOUT) {
            Toast.makeText(context, context.getString(R.string.tts_error_network_timeout), Toast.LENGTH_SHORT).show();
        } else if (i == TextToSpeech.ERROR_NOT_INSTALLED_YET) {
            Toast.makeText(context, context.getString(R.string.tts_error_not_installed), Toast.LENGTH_SHORT).show();
        } else if (i == TextToSpeech.ERROR_SERVICE) {
            Toast.makeText(context, context.getString(R.string.tts_error_service), Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(context, context.getString(R.string.tts_error_unknown), Toast.LENGTH_SHORT).show();
        }
    }

    private void setTTSLanguage() {
        // Locale localeUS = Locale.PRC;
        if(tts==null){
            Toast.makeText(context, context.getString(R.string.tts_uninitialized), Toast.LENGTH_SHORT).show();
            return;
        }
        int result = tts.setLanguage(mapVoice(preferenceManager.getVoice())); // en, ch-zh
        if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
            Toast.makeText(context, context.getString(R.string.tts_language_not_supported), Toast.LENGTH_SHORT).show();
            Toast.makeText(context, context.getString(R.string.tts_setting_language_chinese), Toast.LENGTH_SHORT).show();
            tts.setLanguage(Locale.PRC);
        }
    }
    public void speakTTS(String text) {
        setTTSLanguage();
        tts.setSpeechRate(preferenceManager.getSpeed());
        tts.speak(text, TextToSpeech.QUEUE_FLUSH, null, null);
    }
    private Locale mapVoice(String voice){
        switch (voice){
            case "Cantonese":
                return new Locale("yue");
            case "Mandarin":
                return Locale.PRC;
            case "English":
                return Locale.ENGLISH;
        }
        return new Locale("yue");
    }

}
