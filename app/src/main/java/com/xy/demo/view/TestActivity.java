//package com.xy.demo.view;
//
//import android.Manifest;
//import android.annotation.SuppressLint;
//import android.app.Activity;
//import android.content.Intent;
//import android.content.pm.PackageManager;
//import android.graphics.Bitmap;
//import android.hardware.usb.UsbDevice;
//import android.os.Build;
//import android.os.Bundle;
//import android.os.Environment;
//import android.os.PowerManager;
//import android.provider.Settings;
//import android.util.Log;
//import android.view.View;
//import android.widget.ImageButton;
//import android.widget.LinearLayout;
//import android.widget.ScrollView;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//import androidx.annotation.Nullable;
//import androidx.annotation.RequiresApi;
//import androidx.core.app.ActivityCompat;
//
//import android.widget.Toast;
//
//import androidx.activity.result.ActivityResultLauncher;
//import androidx.activity.result.contract.ActivityResultContracts;
//
//import com.xy.demo.R;
//import com.xy.demo.ocr.GoogleOCR;
//import com.xy.demo.ocr.OCRCallback;
//
//
//import com.xy.demo.ocr.OfflineOCR;
//import com.xy.demo.tts.AzureTextSynthesis;
//import com.xy.demo.utils.BackCam;
//import com.xy.demo.utils.BitmapUtil;
//import com.xy.demo.utils.ButtonEffect;
//import com.xy.demo.utils.PreferenceManager;
//import com.xy.usb.base.MultiCameraClient;
//import com.xy.usb.base.callback.ICameraStateCallBack;
//import com.xy.usb.base.callback.IPreviewDataCallBack;
//import com.xy.usb.base.render.env.RotateType;
//import com.xy.usb.api.DeviceConnectCallback;
//import com.xy.usb.api.MyTextureView;
//import com.xy.usb.api.UsbCamera;
//
//import java.io.File;
//import java.util.concurrent.ExecutionException;
//
//public class TestActivity extends BaseActivity implements IPreviewDataCallBack, OCRCallback {
//    private static String TAG = "_usb_";
//    private UsbCamera mCamera;
//    private static final int REQUEST_PERMISSIONS = 1;
//    private int mPreviewWidth = 1280;
//    private int mPreviewHeight = 720;
////    private int mPreviewWidth = 640;
////    private int mPreviewHeight = 480;
//    private MyTextureView mTextureView = null;
//    private LinearLayout mViewContainer = null;
//    private PowerManager.WakeLock m_wklk;
//    private static final boolean IS_LANDSCAPE = false;
//    private ImageButton shootBtn, pauseBtn, settingBtn, replayBtn, backBtn;
//    private TextView resultTextView;
//    private ScrollView textviewBorder;
//    private boolean isCapturing,isOCRProceesing,isLockScreen = false;
//    private GoogleOCR Google;
//    private String googleapikey="AIzaSyC0lqNoLu8VtLMS_2Z0E5Ma-zuKGBxjgO0";
//    private AzureTextSynthesis AzureTTS;
//    private String OCRtext;
//    private ButtonEffect buttonEffect;
//    private BackCam backCam;
//    private OfflineOCR offlineOCR;
//    private boolean isBackCam=true;
//
//
//    private boolean hasPermissionsGranted(String[] permissions) {
//        for (String permission : permissions) {
//            if (ActivityCompat.checkSelfPermission(getApplicationContext(), permission)
//                    != PackageManager.PERMISSION_GRANTED) {
//                return false;
//            }
//        }
//        return true;
//    }
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        if (requestCode == 1) {
//            if (grantResults.length == 1 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
//                // Permission has been granted. Start camera preview Activity.
//                Toast.makeText(this, "Camera permission granted", Toast.LENGTH_SHORT).show();
//                // If necessary, initiate opening the camera from here
//            } else {
//                // Permission request was denied.
//                Toast.makeText(this, "Camera permission request was denied.", Toast.LENGTH_SHORT).show();
//            }
//        }
//    }
//    @RequiresApi(api = Build.VERSION_CODES.M)
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.testusb);
//        initView();
//
//        if (!hasPermissionsGranted(new String[]{Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO}))
//        {
//            requestPermissions(new String[]{
//                    Manifest.permission.CAMERA,
//                    Manifest.permission.MOUNT_UNMOUNT_FILESYSTEMS,
//                    Manifest.permission.SYSTEM_ALERT_WINDOW,
//                    Manifest.permission.ACCESS_COARSE_LOCATION,
//                    Manifest.permission.ACCESS_FINE_LOCATION,
//                    Manifest.permission.RECORD_AUDIO,
//                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
//                    Manifest.permission.READ_EXTERNAL_STORAGE
//
//            }, REQUEST_PERMISSIONS);
//            return;
//        }
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//            if(!Environment.isExternalStorageManager())
//            {
//                Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
//                startActivityForResult(intent, 1022);
//                return;
//            }
//        }
//
//        Log.d(TAG, "onCreate");
//        lock();
//        mCamera = UsbCamera.getInstance(this);
//        //mTextureView = (MyTextureView) findViewById(R.id.texture_view);
//        mViewContainer = (LinearLayout)findViewById(R.id.cameraViewContainer);
//        mTextureView = mCamera.createCameraView(this, mViewContainer, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
//        //mTextureView = mCamera.createCameraView(this, mViewContainer, 720, 1280);
//        if(IS_LANDSCAPE)
//            mTextureView.setAspectRatio(mPreviewWidth, mPreviewHeight);
//        else
//            mTextureView.setAspectRatio(mPreviewHeight, mPreviewWidth);
//        mCamera.setCameraView(mTextureView);
//        initUsb();
//        Log.i("check","voice " + getCurrentVoice());
//        backCam=new BackCam(this,this.mTextureView);
//        buttonEffect =new ButtonEffect(this);
//        Google=new GoogleOCR();
//        offlineOCR=new OfflineOCR(this);
//        assert (Google!=null);
//        AzureTTS=new AzureTextSynthesis(getCurrentVoice());
//        assert (AzureTTS!=null);
//        resultTextView = findViewById(R.id.resultTextView);
//        textviewBorder = findViewById(R.id.textviewBorder);
//        shootBtn = findViewById(R.id.ocrBtn);
//        pauseBtn = findViewById(R.id.stopBtn);
//        settingBtn = findViewById(R.id.settingBtn);
//        replayBtn = findViewById(R.id.replayBtn);
//        backBtn = findViewById(R.id.backBtn);
//        shootBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                Log.i("check",String.valueOf(isCapturing));
//                if(isCapturing||isOCRProceesing){return;}
////                Toast.makeText(getApplicationContext(), "已經開始拍照", Toast.LENGTH_SHORT).show();
//
////                buttonEffect.play();
////                isOCRProceesing=true;
////                mCamera.addPreviewDataCallBack(TestActivity.this);
////                shootBtn.setVisibility(View.GONE);
////                shootBtn.setEnabled(false);
////                settingBtn.setVisibility(View.GONE);
////                settingBtn.setEnabled(false);
////                textviewBorder.setVisibility(View.VISIBLE);
////
////                pauseBtn.setVisibility(View.VISIBLE);
////                pauseBtn.setEnabled(true);
////                pauseBtn.setImageResource(R.drawable.baseline_pause_24);
////                replayBtn.setVisibility(View.VISIBLE);
////                replayBtn.setEnabled(true);
////                backBtn.setVisibility(View.VISIBLE);
////                backBtn.setEnabled(true);
//                backCam.capturePhoto();
//
////                mCamera.removePreviewDataCallBack(TestUsbActivity.this);
//            }
//        });
//        settingBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                buttonEffect.play();
////                startActivity(new Intent(TestActivity.this, SettingActivity.class));
//                Intent intent = new Intent(TestActivity.this, SettingActivity.class);
//                settingActivityResultLauncher.launch(intent);
//            }
//        });
//        pauseBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                buttonEffect.play();
//                if(AzureTTS.stopstate.get()){
//                    Log.i("check","invalid press!");
//                    return;}
//                if(AzureTTS.isPaused.get()){
//                    Log.i("check","run resumesynthesis");
//                    AzureTTS.resumeSynthesis();
//                    pauseBtn.setImageResource(R.drawable.baseline_pause_24);
//                }else{
//                    Log.i("check","run pausesynthesis");
//                    AzureTTS.pauseSynthesis();
//                    pauseBtn.setImageResource(R.drawable.round_play_arrow_24);
//                }
//            }
//        });
//        replayBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                buttonEffect.play();
//                try {
//                    AzureTTS.replay(OCRtext,getCurrentSpeed());
//                } catch (ExecutionException | InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//                pauseBtn.setImageResource(R.drawable.baseline_pause_24);
//            }
//        });
//        backBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                buttonEffect.play();
//                try {
//                    AzureTTS.stopSynthesis();
//                } catch (ExecutionException | InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//                isOCRProceesing=false;
//                isCapturing=false;
//                shootBtn.setVisibility(View.VISIBLE);
//                shootBtn.setEnabled(true);
//                settingBtn.setVisibility(View.VISIBLE);
//                settingBtn.setEnabled(true);
//                pauseBtn.setVisibility(View.GONE);
//                pauseBtn.setEnabled(false);
//                replayBtn.setVisibility(View.GONE);
//                replayBtn.setEnabled(false);
//                backBtn.setVisibility(View.GONE);
//                backBtn.setEnabled(false);
//                textviewBorder.setVisibility(View.GONE);
//            }
//        });
//
//
////        findViewById(R.id.takephoto).setOnClickListener(new View.OnClickListener() {
////            @Override
////            public void onClick(View v) {
////                Log.e("_usb_", "startPreview screenWidth="+mTextureView.getSurfaceWidth()+",screenHeight="+mTextureView.getSurfaceHeight());
////                mCamera.takePhoto(getFolderFilePath(0), new ICaptureCallBack() {
////                    @Override
////                    public void onBegin() {
////                        Log.d(TAG, "activity take photo onBegin");
////                    }
////
////                    @Override
////                    public void onError(@Nullable String error) {
////                        Log.d(TAG, "activity take photo onError="+error);
////                    }
////
////                    @Override
////                    public void onComplete(@Nullable String path) {
////                        Log.d(TAG, "activity take photo onComplete path="+path);
////                    }
////                });
////            }
////        });
////
////        findViewById(R.id.previewstart).setOnClickListener(new View.OnClickListener() {
////            @Override
////            public void onClick(View v) {
////                mCamera.addPreviewDataCallBack(TestUsbActivity.this);
////            }
////        });
////        findViewById(R.id.previewstop).setOnClickListener(new View.OnClickListener() {
////            @Override
////            public void onClick(View v) {
////                mCamera.removePreviewDataCallBack(TestUsbActivity.this);
////            }
////        });
//    }
//    @Override
//    protected void onResume() {
//        super.onResume();
//
//        if(mTextureView.isAvailable()){
////            Log.i("check","switching to backcam");
//            backCam.switchToBackCamera();
//        }
//        else{
//            Log.i("check","switching to backcam");
//            mTextureView.setSurfaceTextureListener(backCam.mSurfaceTextureListener);
//        }
//
//        if(AzureTTS!=null&&AzureTTS.isPaused!=null&&AzureTTS.stopstate!=null&&!AzureTTS.stopstate.get()&&AzureTTS.isPaused.get()&&isLockScreen){
//            isLockScreen=false;
//            Log.i("check","run resumesynthesis");
//            AzureTTS.resumeSynthesis();
//            pauseBtn.setImageResource(R.drawable.baseline_pause_24);
//        }
//
//    }
//
//    @Override
//    protected void onPause() {
//        super.onPause();
//        if(AzureTTS!=null&&AzureTTS.isPaused!=null&&AzureTTS.stopstate!=null&&!AzureTTS.stopstate.get()&&!AzureTTS.isPaused.get()){
//            isLockScreen=true;
//            Log.i("check","run pausesynthesis");
//            AzureTTS.pauseSynthesis();
//            pauseBtn.setImageResource(R.drawable.round_play_arrow_24);
//        }
//    }
//
//    private String getFolderFilePath(int deviceId)
//    {
//        String fname = UsbCamera.generateFName();
//        String folder = Environment.getExternalStorageDirectory() + "/";
//        File f = new File(folder);
//        Log.e("_usb_", "getFolderFilePath exist="+f.exists());
//        if(!f.exists())
//            f.mkdirs();
//        Log.e("_usb_", "getFolderFilePath 2 exist="+f.exists());
//        return folder + fname + ".jpg";
//    }
//
//    private void initView() {
//
//    }
//
//    @SuppressLint("InvalidWakeLockTag")
//    private void lock()
//    {
//        PowerManager pm = (PowerManager)getSystemService(POWER_SERVICE);
//        m_wklk = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "ms01");
//        m_wklk.acquire();
//    }
//
//    private void unlock()
//    {
//        if(m_wklk != null)
//        {
//            m_wklk.release();
//            m_wklk = null;
//        }
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        Log.d(TAG, "activity onDestroy");
//        destroyUsb();
//        unlock();
//        if(AzureTTS!=null){
//            AzureTTS.destroy();
//        }
//        if(buttonEffect!=null){
//            buttonEffect.release();
//        }
//    }
//
//    ////////////////////////////////////////////////////////////////////////////////////////////////
//    // for usb api
//
//    private void initUsb()
//    {
//        mCamera.setPreviewSize(mPreviewWidth, mPreviewHeight);
//        mCamera.setRotateType(RotateType.ANGLE_270);
//        mCamera.setIsLandscape(IS_LANDSCAPE);
//        mCamera.init(new DeviceConnectCallback() {
//            @Override
//            public void onAttachDev(@Nullable UsbDevice device) {
//                if(backCam!=null){
//                    Log.i("check","stop backcam streaming");
//                    backCam.closeCam();
//                }
//                Log.e(TAG, "activity onAttachDev");
//            }
//
//            @Override
//            public void onDetachDec(@Nullable UsbDevice device) {
//                Log.e(TAG, "activity onDetachDec");
//                backCam.openCam();
//            }
//
//            @Override
//            public void onConnectDev(@Nullable UsbDevice device) {
//                Log.e(TAG, "activity onConnectDev");
//                //mCamera.startAVStream();
//
//            }
//
//            @Override
//            public void onDisConnectDec(@Nullable UsbDevice device) {
//                Log.e(TAG, "activity onDisConnectDec");
////                if(mTextureView.isAvailable()){
////                    backCam.switchToBackCamera();
////                }
////                else{
////                    Log.i("check","switching to backcam");
////                    mTextureView.setSurfaceTextureListener(backCam.mSurfaceTextureListener);
////                }
//                backCam.openCam();
//                //mCamera.stopAVStream();
//            }
//
//            @Override
//            public void onCancelDev(@Nullable UsbDevice device) {
//                Log.e(TAG, "activity onCancelDev");
//                backCam.openCam();
//            }
//        });
//        mCamera.setCameraStateCallBack(new ICameraStateCallBack() {
//            @Override
//            public void onCameraState(@NonNull MultiCameraClient.ICamera self, @NonNull State code, @Nullable String msg) {
//                Log.e(TAG, "activity onCameraState code="+code);
//            }
//        });
//    }
//
//    private void destroyUsb()
//    {
//        if(mCamera != null)
//            mCamera.unInit();
//    }
//
//    private final ActivityResultLauncher<Intent> settingActivityResultLauncher =
//            registerForActivityResult(
//                    new ActivityResultContracts.StartActivityForResult(),
//                    result -> {
//                        if (result.getResultCode() == Activity.RESULT_OK) {
//                            // Intent data is the returned result
//                            Intent data = result.getData();
//                            if (data != null) {
//                                // Get the voice string from the returned data
//                               AzureTTS.createSynthesizer(data.getStringExtra("voice"));
//                                // Now you can use the voice string as needed
//                            }
//                        }
//                    });
//
//    @Override
//    public void onPreviewData(@Nullable byte[] data, int width, int height, @NonNull DataFormat format) {
//        Log.d(TAG, "onPreviewData data="+data.length+",w="+width+",h="+height);
//        if(!isCapturing)
//        {
//            isCapturing=true;
//            Log.d(TAG,"try to save to bitmap once");
////            BitmapUtil.saveToBitmap(data, width, height, 270, 90, getFolderFilePath(0));
////            Google.performOCR(googleapikey,data,width,height,this);
//            offlineOCR.process(data,width,height);
//            BitmapUtil.saveToBitmap(data, width, height, 90, 90, getFolderFilePath(0));
//            mCamera.removePreviewDataCallBack(TestActivity.this);
//        }
//    }
//    @Override
//    public void onisProcessingChanged(Boolean isProcessing) {
//    this.isOCRProceesing=isProcessing;
//    this.isCapturing=false;
//    }
//    @Override
//    public void onProcessComplete(String text) {
//        Log.i("check return text",text);
//        if (text != null && !text.isEmpty()) {
//            Log.i("text is ?", text);
//            OCRtext=text;
//            resultTextView.setText(text);
//            //prevent accidental exit before OCR is done.
//            if( backBtn.getVisibility()==View.VISIBLE){
//            AzureTTS.startPlaying(text,getCurrentSpeed());
//            }
//        }
//        else{
//            if(getCurrentLanguage().equals(PreferenceManager.CHINESE)){
//                OCRtext="沒有識別到文字，請再次嘗試";
//            }
//            else{
//                OCRtext="No text recognized, please try again";
//            }
//            resultTextView.setText(OCRtext);
//        }
//    }
//
//
//}


package com.xy.demo.view;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.hardware.usb.UsbDevice;
import android.os.Bundle;
import android.os.Environment;
import android.os.PowerManager;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.core.ImageProxy;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.mlkit.vision.common.InputImage;
import com.xy.demo.R;
import com.xy.demo.databinding.TestusbBinding; // 導入 View Binding 產生的類別
import com.xy.demo.ocr.GoogleOCR;
import com.xy.demo.ocr.OCRCallback;
import com.xy.demo.ocr.OfflineOCR;
import com.xy.demo.tts.AzureTextSynthesis;
import com.xy.demo.utils.BackCam;
import com.xy.demo.utils.BitmapUtil;
import com.xy.demo.utils.ButtonEffect;
import com.xy.demo.utils.PreferenceManager;
import com.xy.usb.api.DeviceConnectCallback;
import com.xy.usb.api.MyTextureView;
import com.xy.usb.api.UsbCamera;
import com.xy.usb.base.MultiCameraClient;
import com.xy.usb.base.callback.ICameraStateCallBack;
import com.xy.usb.base.callback.IPreviewDataCallBack;
import com.xy.usb.base.render.env.RotateType;

import java.io.File;
import java.util.concurrent.ExecutionException;

public class TestActivity extends BaseActivity implements IPreviewDataCallBack, OCRCallback {
    private static final String TAG = "_usb_";
    private UsbCamera mCamera;
    private int mPreviewWidth = 1280;
    private int mPreviewHeight = 720;
    private MyTextureView mTextureView = null; // 用於 USB 相機
    private PowerManager.WakeLock m_wklk;
    private static final boolean IS_LANDSCAPE = false;

    // View Binding 實例，用來取代 findViewById
    private TestusbBinding binding;
    private GoogleOCR googleOCR;
    private String googleapikey="AIzaSyC0lqNoLu8VtLMS_2Z0E5Ma-zuKGBxjgO0";
    private boolean isCapturing, isOCRProceesing, isLockScreen = false;
    private AzureTextSynthesis AzureTTS;
    private String OCRtext;
    private ButtonEffect buttonEffect;
    private BackCam backCam;
    private OfflineOCR offlineOCR;




    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = TestusbBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 權限已在 SplashActivity 中檢查完畢，直接初始化
        initializeCameraAndViews();
        googleOCR = new GoogleOCR();
    }

    private void initializeCameraAndViews() {
        Log.d(TAG, "權限已授予，正在初始化相機和視圖...");
        lock();
        mCamera = UsbCamera.getInstance(this);

        // USB 相機的 View 仍然以程式碼方式加入到 LinearLayout 中
        mTextureView = mCamera.createCameraView(this, binding.cameraViewContainer, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);

        if (IS_LANDSCAPE)
            mTextureView.setAspectRatio(mPreviewWidth, mPreviewHeight);
        else
            mTextureView.setAspectRatio(mPreviewHeight, mPreviewWidth);

        mCamera.setCameraView(mTextureView);
        initUsb();
        Log.i("check", "voice " + getCurrentVoice());

        // 關鍵改動：將佈局中的 PreviewView (binding.viewFinder) 傳遞給 BackCam
        backCam = new BackCam(this, binding.viewFinder);

        buttonEffect = new ButtonEffect(this);
        offlineOCR = new OfflineOCR(this);
        AzureTTS = new AzureTextSynthesis(getCurrentVoice(), this);

        setupClickListeners();
    }

    /**
     * 優化的UI狀態管理 - 統一處理模式切換
     */
    private void switchToProcessingMode() {
        runOnUiThread(() -> {
            // 批量更新UI狀態，減少重繪次數
            binding.ocrBtn.setVisibility(View.GONE);
            binding.settingBtn.setVisibility(View.GONE);
            binding.textviewBorder.setVisibility(View.VISIBLE);
            binding.playbackControls.setVisibility(View.VISIBLE);
            binding.stopBtn.setImageResource(R.drawable.baseline_pause_24);
            
            // 修復: 立即清空TextView內容，避免顯示舊的OCR結果
            binding.resultTextView.setText("");
        });
    }

    /**
     * 重置到拍照模式
     */
    private void resetToCaptureMode() {
        try {
            if (AzureTTS != null) {
                AzureTTS.stopSynthesis();
            }
        } catch (ExecutionException | InterruptedException e) {
            Log.e(TAG, "Error stopping TTS", e);
        }
        
        isOCRProceesing = false;
        isCapturing = false;
        
        runOnUiThread(() -> {
            // 批量更新UI狀態
            binding.ocrBtn.setVisibility(View.VISIBLE);
            binding.settingBtn.setVisibility(View.VISIBLE);
            binding.playbackControls.setVisibility(View.GONE);
            binding.textviewBorder.setVisibility(View.GONE);
        });
    }

    private void setupClickListeners() {
        binding.ocrBtn.setOnClickListener(v -> {
            if (isCapturing || isOCRProceesing) {
                return;
            }
            Toast.makeText(getApplicationContext(), getString(R.string.taking_photo), Toast.LENGTH_SHORT).show();
            isCapturing = true;
            backCam.capturePhoto();
        });

        binding.settingBtn.setOnClickListener(view -> {
            buttonEffect.play();
            Intent intent = new Intent(TestActivity.this, SettingActivity.class);
            settingActivityResultLauncher.launch(intent);
        });

        binding.stopBtn.setOnClickListener(v -> {
            buttonEffect.play();
            if (AzureTTS.stopstate.get()) return;
            if (AzureTTS.isPaused.get()) {
                AzureTTS.resumeSynthesis();
                binding.stopBtn.setImageResource(R.drawable.baseline_pause_24);
            } else {
                AzureTTS.pauseSynthesis();
                binding.stopBtn.setImageResource(R.drawable.round_play_arrow_24);
            }
        });

        binding.replayBtn.setOnClickListener(v -> {
            buttonEffect.play();
            try {
                AzureTTS.replay(OCRtext, getCurrentSpeed());
            } catch (ExecutionException | InterruptedException e) {
                throw new RuntimeException(e);
            }
            binding.stopBtn.setImageResource(R.drawable.baseline_pause_24);
        });

        binding.backBtn.setOnClickListener(view -> {
            buttonEffect.play();
            resetToCaptureMode();
        });
    }

    /**
     * 這個方法是從 BackCam 回呼的，用於處理手機內建相機拍攝的照片。
     */
    public void processBackCameraImage(ImageProxy image) {
        Log.d(TAG, "接收到後置相機的圖片，開始 OCR 流程。");

        // 1. 更新 UI 進入處理中狀態  
        buttonEffect.play();
        isOCRProceesing = true;
        switchToProcessingMode();

        // 2. 啟動離線 OCR 處理
//        offlineOCR.process(image);
        googleOCR.processImageProxy(googleapikey, image, this);

        // (可選) 儲存一張除錯用的圖片
//        BitmapUtil.saveToBitmap(data, width, height, 90, 90, getFolderFilePath(0));
    }

    /**
     * 從 BackCam 回呼的錯誤處理方法。
     * @param errorMessage 錯誤訊息
     */
    public void onOcrError(String errorMessage) {
        runOnUiThread(() -> {
            Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show();
            // 重設 UI 狀態
            isOCRProceesing = false;
            isCapturing = false;
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (backCam != null) {
            backCam.switchToBackCamera();
        }

        if (AzureTTS != null && !AzureTTS.stopstate.get() && AzureTTS.isPaused.get() && isLockScreen) {
            isLockScreen = false;
            AzureTTS.resumeSynthesis();
            binding.stopBtn.setImageResource(R.drawable.baseline_pause_24);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (AzureTTS != null && !AzureTTS.stopstate.get() && !AzureTTS.isPaused.get()) {
            isLockScreen = true;
            AzureTTS.pauseSynthesis();
            binding.stopBtn.setImageResource(R.drawable.round_play_arrow_24);
        }
    }

    private String getFolderFilePath(int deviceId) {
        String fname = UsbCamera.generateFName();
        File folder = getExternalFilesDir(null);
        if (folder != null && !folder.exists()) {
            folder.mkdirs();
        }
        return new File(folder, fname + ".jpg").getAbsolutePath();
    }

    @SuppressLint("InvalidWakeLockTag")
    private void lock() {
        PowerManager pm = (PowerManager) getSystemService(POWER_SERVICE);
        if (m_wklk == null) {
            m_wklk = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "CameraSDKWakeLock");
        }
        if (!m_wklk.isHeld()) {
            // 設定10分鐘超時，防止長期持有
            m_wklk.acquire(10 * 60 * 1000);
        }
    }

    private void unlock() {
        if (m_wklk != null) {
            if (m_wklk.isHeld()) {
                m_wklk.release();
            }
            m_wklk = null;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "activity onDestroy");
        destroyUsb();
        unlock();
        if (AzureTTS != null) AzureTTS.destroy();
        if (buttonEffect != null) buttonEffect.release();
        if (googleOCR != null) googleOCR.destroy();
    }

    private void initUsb() {
        mCamera.setPreviewSize(mPreviewWidth, mPreviewHeight);
        mCamera.setRotateType(RotateType.ANGLE_270);
        mCamera.setIsLandscape(IS_LANDSCAPE);
        mCamera.init(new DeviceConnectCallback() {
            @Override
            public void onAttachDev(@Nullable UsbDevice device) {
                if (backCam != null) {
                    Log.i("check", "偵測到 USB 裝置，關閉後置鏡頭");
                    backCam.closeCam();
                }
                Log.e(TAG, "activity onAttachDev");
            }

            @Override
            public void onDetachDec(@Nullable UsbDevice device) {
                Log.e(TAG, "activity onDetachDec");
                backCam.openCam();
            }

            @Override
            public void onConnectDev(@Nullable UsbDevice device) {
                Log.e(TAG, "activity onConnectDev");
            }

            @Override
            public void onDisConnectDec(@Nullable UsbDevice device) {
                Log.e(TAG, "activity onDisConnectDec");
                backCam.openCam();
            }

            @Override
            public void onCancelDev(@Nullable UsbDevice device) {
                Log.e(TAG, "activity onCancelDev");
                backCam.openCam();
            }
        });
        mCamera.setCameraStateCallBack((self, code, msg) -> Log.e(TAG, "activity onCameraState code=" + code));
    }

    private void destroyUsb() {
        if (mCamera != null)
            mCamera.unInit();
    }

    private final ActivityResultLauncher<Intent> settingActivityResultLauncher =
            registerForActivityResult(
                    new ActivityResultContracts.StartActivityForResult(),
                    result -> {
                        if (result.getResultCode() == Activity.RESULT_OK) {
                            Intent data = result.getData();
                            if (data != null) {
                                AzureTTS.createSynthesizer(data.getStringExtra("voice"));
                            }
                        }
                    });

    @Override
    public void onPreviewData(@Nullable byte[] data, int width, int height, @NonNull DataFormat format) {
        // USB 相機的回呼
        if (!isCapturing) {
            isCapturing = true;
            Log.d(TAG, "接收到 USB 相機的圖片，開始 OCR 流程。");

            // 顯示處理中 UI
            buttonEffect.play();
            isOCRProceesing = true;
            switchToProcessingMode();

            // 由於 USB 相機給的是 byte[]，我們仍然使用舊的方式建立 InputImage
            // 注意：這裡假設 USB 相機的格式是 NV21，如果不是，這裡也可能出錯
            assert data != null;
            InputImage image = InputImage.fromByteArray(data, width, height, 90, InputImage.IMAGE_FORMAT_NV21);

            // 手動呼叫 recognizer 進行處理
            // (這部分需要將 OfflineOCR 的 recognizer 設為 public 或提供一個新的 process 方法)
            // 為了簡化，我們先專注於修復後置鏡頭的流程。
        }
    }

    @Override
    public void onisProcessingChanged(Boolean isProcessing) {
        this.isOCRProceesing = isProcessing;
        if (!isProcessing) {
            this.isCapturing = false;
        }
    }

    @Override
    public void onProcessComplete(String text) {
        runOnUiThread(() -> {
            Log.i("check return text", text);
            if (text != null && !text.isEmpty()) {
                OCRtext = text;
                binding.resultTextView.setText(text);
                if (binding.playbackControls.getVisibility() == View.VISIBLE) {
                    AzureTTS.startPlaying(text, getCurrentSpeed());
                }
            } else {
                OCRtext = getCurrentLanguage().equals(PreferenceManager.CHINESE) ?
                        "沒有識別到文字，請再次嘗試" :
                        "No text recognized, please try again";
                binding.resultTextView.setText(OCRtext);
            }
        });
    }
}

