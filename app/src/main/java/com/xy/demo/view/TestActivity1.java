//package com.xy.demo.view;
//
//
//import android.content.Intent;
//import android.content.pm.PackageManager;
//import android.os.Build;
//import android.os.Bundle;
//import android.os.PowerManager;
//import android.util.Log;
//import android.widget.ImageButton;
//import android.widget.ScrollView;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//import androidx.annotation.Nullable;
//import androidx.annotation.RequiresApi;
//import androidx.core.app.ActivityCompat;
//
//import com.xy.demo.ocr.GoogleOCR;
//import com.xy.demo.ocr.OCRCallback;
//import com.xy.demo.R;
//
//
////import java.util.Base64;
//import android.view.View;
//
//import android.graphics.SurfaceTexture;
//import android.hardware.usb.UsbDevice;
//import android.os.Handler;
//import android.os.Message;
//import android.os.SystemClock;
//import android.view.Surface;
//import android.view.TextureView;
//
//import com.xy.demo.utils.PreferenceManager;
//import com.xy.usb.UsbCameraModule;
//import com.serenegiant.usb.USBMonitor;
//
//public class TestActivity1 extends BaseActivity implements UsbCameraModule.IUsbCameraCallback, OCRCallback {
//    private static final int PREV_WIDTH = 1280;
//    private static final int PREV_HEIGHT = 720;
//    protected UsbCameraModule mUsb;
//    protected TextureView mTexture = null;
//    protected Surface mPreviewSurface;
//    protected int mPreviewWidth = 1;
//    protected int mPreviewHeight = 1;
//    protected boolean mbAutoReset = true;
//    protected long mLastFrameTime = 0L;
//    private static final int MSG_UVC_OPENFAIL = 7001;
//    private static final int MSG_UVC_OPEN = 7002;
//    private static final int MSG_UVC_CLOSE = 7003;
//    private static final int MSG_CLOSE_FLOATVIEW = 7010;
//    private ScrollView textviewBorder;
//    private AutoSizePreview mAutoSizePreview = null;
//    private TextView resultTextView;
//    // private TextToSpeech textToSpeech;
//    private ImageButton shootBtn, stopBtn, settingBtn, replayBtn, backBtn;
//    private boolean isCapturing,isOCRProceesing = false;
//    private GoogleOCR Google= new GoogleOCR();
//    private String googleapikey="AIzaSyC0lqNoLu8VtLMS_2Z0E5Ma-zuKGBxjgO0";
//
//    private String lastDetectedText = "";
//    private PreferenceManager preferenceManager;
//    private String savedLanguage;
//    private float savedSpeed;
//    private PowerManager.WakeLock m_wklk;
//
//    private Handler mHandler = new Handler() {
//        public void handleMessage(Message var1) {
//            if (var1.what == 7002) {
//                TestActivity1.this.initUsb();
//            } else if (var1.what == 7003) {
//                TestActivity1.this.disConncetUsb();
//            } else if (var1.what == 7010) {
//                TestActivity1.this.destroyFloatView();
//            } else {
//                if (var1.what == 7001) {
//                    TestActivity1.this.destroyUsb();
//                    TestActivity1.this.mHandler.sendEmptyMessageDelayed(7002, 2000L);
//                }
//
//            }
//        }
//    };
//    USBMonitor.OnDeviceConnectListener mListener = new USBMonitor.OnDeviceConnectListener() {
//        public void onAttach(UsbDevice var1) {
//            // if(camstate!=-1){
//            // camstate=-1;
//            // }
//
//            // mUsb.doDisconnectUsbCamera();
//            Log.i("_usb_", "doattached");
////            mPreviewSurface.release();
////            mPreviewSurface = null;
//            // initTextureView(mAutoSizePreview);
//        }
//
//        public void onDettach(UsbDevice var1) {
//            // if(camstate!=1){
//            mUsb.doDisconnectUsbCamera();
//            mUsb.doDisconnectUsbCamera();
//            // initTextureView(mAutoSizePreview);
//            Log.i("_usb_", "dodisconnected");
//        }
//
//        public void onConnect(UsbDevice var1, USBMonitor.UsbControlBlock var2, boolean var3) {
////            while(mAutoSizePreview!=null&&mAutoSizePreview.getSurfaceTexture()!=null){
//
//            if (TestActivity1.this.mUsb != null &&mAutoSizePreview.getSurfaceTexture()!=null) {
//                Log.i("_usb_", "usbconnected!");
//                mPreviewSurface = new Surface(mAutoSizePreview.getSurfaceTexture());
//                // if (TestActivity.this.mPreviewSurface != null &&
//                // TestActivity.this.mPreviewSurface.isValid()) {
//                Log.e("_usb_", "++++++ onConnect mPreviewSurface=" + TestActivity1.this.mPreviewSurface);
//                Log.e("check", "++++++ onConnect mPreviewSurface=" + TestActivity1.this.mPreviewSurface);
//                TestActivity1.this.startPreview(TestActivity1.this.mPreviewSurface);
//                // }
////                }
//            }
//        }
//
//        public void onDisconnect(UsbDevice var1, USBMonitor.UsbControlBlock var2) {
//            Log.i("_usb_", "disconnected");
//        }
//
//        public void onCancel(UsbDevice var1) {
//            Log.i("_usb_", "canceled");
//        }
//
//        public void onGetCameraConfig(int var1, int var2, String var3) {
//            Log.e("_usb_", "camera pid=" + var1 + ",vid=" + var2 + ",reso=" + var3);
//        }
//
//        public void onPreviewFail() {
//            Log.e("_usb_", "++++++++++onPreviewFail");
//            TestActivity1.this.mHandler.sendEmptyMessage(7001);
//        }
//
//        public void onPermissionRequest() {
//            Log.i("_usb_", "onpermission");
//        }
//    };
//    TextureView.SurfaceTextureListener mSurfaceTextureListener = new TextureView.SurfaceTextureListener() {
//        public void onSurfaceTextureAvailable(SurfaceTexture var1, int var2, int var3) {
//            Log.i("_usb_", "textureavailable");
//            if (TestActivity1.this.mPreviewSurface == null) {
//                TestActivity1.this.mPreviewSurface = new Surface(var1);
//                Log.i("_usb_", "nullsurface");
//            }
//            Log.d("_usb_", "onSurfaceTextureAvailable startPreview");
//            TestActivity1.this.startPreview(TestActivity1.this.mPreviewSurface);
//        }
//
//        public void onSurfaceTextureSizeChanged(SurfaceTexture var1, int var2, int var3) {
//        }
//
//        public boolean onSurfaceTextureDestroyed(SurfaceTexture var1) {
//            Log.d("_usb_", "onSurfaceTextureDestroyed");
//            return true;
//        }
//
//        public void onSurfaceTextureUpdated(SurfaceTexture var1) {
//        }
//    };
//
//    protected void initTextureView(TextureView var1) {
//        this.mHandler.sendEmptyMessage(7002);
//        if (mTexture == null) {
//            Log.i("check", "mTexure is destroyed");
//            this.mTexture = var1;
//            this.mTexture.setSurfaceTextureListener(this.mSurfaceTextureListener);
//        }
//    }
//
//    protected void forceReboot() {
//        this.mHandler.sendEmptyMessage(7001);
//    }
//
//    public void startRecord(String var1) {
//        if (this.mUsb != null) {
//            this.mUsb.startUsbCamRecording(var1);
//        }
//
//    }
//
//    public void stopRecord() {
//        if (this.mUsb != null) {
//            this.mUsb.stopUsbCamRecording();
//        }
//
//    }
//
//    public boolean isRecording() {
//        return this.mUsb != null ? this.mUsb.isRecording() : false;
//    }
//
//    private void onResumeUsb() {
//        this.registerUsb();
//        Log.i("check", "resuming usb");
//        if (this.mUsb != null && this.mPreviewSurface != null && this.mPreviewSurface.isValid()) {
//            Log.i("check", "running preview");
//            this.startPreview(this.mPreviewSurface);
//        } else {
//            if (this.mUsb == null) {
//                Log.e("check", "usb is null");
//            } else if (this.mPreviewSurface == null) {
//                Log.e("check", "surface is null");
//            } else if (this.mPreviewSurface.isValid() == false) {
//                Log.e("check", "surface is invalid");
//            }
//        }
//
//    }
//
//    private void onPauseUsb() {
//        this.stopPreview();
//        this.unregisterUsb();
//    }
//
//    private void destroyUsb() {
//        this.stopPreview();
//        this.unregisterUsb();
//        this.mHandler.sendEmptyMessageDelayed(7003, 30L);
//    }
//
//    private void initUsb() {
//        if (this.mUsb == null) {
//            Log.i("check", "initializing usb");
//            this.mUsb = UsbCameraModule.getInstance(this.getApplicationContext());
//            this.mUsb.setBandWidth(0.5F);
//            this.mUsb.init(this.mPreviewWidth, this.mPreviewHeight, this);
//        }
//        this.registerUsb();
//    }
//
//    private void registerUsb() {
//        if (this.mUsb != null) {
//            Log.i("_usb_", "registering usb" + String.valueOf(mListener == null));
//            this.mUsb.registerUsbMonitor(this.mListener);
//        }
//
//    }
//
//    private void unregisterUsb() {
//        if (this.mUsb != null) {
//            this.mUsb.unRegisterUsbMonitor();
//        }
//
//    }
//
//    private void startPreview(Surface var1) {
//        this.stopStream();
//        if (this.mUsb != null) {
//            Log.i("_usb_", "teststartPreview");
//            this.startStream();
//            this.mUsb.startPreview(var1);
//
//        } else {
//            // Log.i("_usb_", String.valueOf(backcam));
//            // setupCamera(mPreviewWidth, mPreviewHeight);
//            // openCamera();
//        }
//
//    }
//
//    private void stopPreview() {
//        if (this.mUsb != null) {
//            this.mUsb.stopPreview();
//        }
//
//    }
//
//    private void disConncetUsb() {
//        if (this.mUsb != null) {
//            this.mUsb.destory();
//            this.mUsb = null;
//        }
//
//        this.initTextureView(mAutoSizePreview);
//        // onResumeUsb();
//    }
//
//    private void startStream() {
//        if (this.mUsb != null) {
//            this.mLastFrameTime = 0L;
//            this.mUsb.setUsbCameraFrameCallback(new UsbCameraModule.IUsbFrameCallback() {
//                public void onFrame(byte[] var1, int var2, int var3, int var4) {
//                    if (TestActivity1.this.mbAutoReset) {
//                        if (TestActivity1.this.mLastFrameTime > 0L
//                                && SystemClock.elapsedRealtime() - TestActivity1.this.mLastFrameTime > 3000L) {
//                            TestActivity1.this.mLastFrameTime = SystemClock.elapsedRealtime();
//                            TestActivity1.this.forceReboot();
//                            return;
//                        }
//
//                        TestActivity1.this.mLastFrameTime = SystemClock.elapsedRealtime();
//                    }
//                    TestActivity1.this.onFrameDetect(var1, var3, var4);
//                }
//            });
//        }
//
//    }
//
//    private void stopStream() {
//        if (this.mUsb != null) {
//            this.mUsb.setUsbCameraFrameCallback((UsbCameraModule.IUsbFrameCallback) null);
//        }
//
//    }
//    @RequiresApi(api = Build.VERSION_CODES.M)
//    @Override
//    protected void onCreate(@Nullable Bundle savedInstanceState) {
//
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.testusb);
//        mPreviewWidth = PREV_WIDTH;
//        mPreviewHeight = PREV_HEIGHT;
//        mAutoSizePreview = (AutoSizePreview) findViewById(R.id.surfaceview);
//        resultTextView = findViewById(R.id.resultTextView);
//        shootBtn = findViewById(R.id.ocrBtn);
//        stopBtn = findViewById(R.id.stopBtn);
//        settingBtn = findViewById(R.id.settingBtn);
//        replayBtn = findViewById(R.id.replayBtn);
//        backBtn = findViewById(R.id.backBtn);
////        waitingText = getString(R.string.waiting_result);
//        textviewBorder = findViewById(R.id.textviewBorder);
//        initTextureView(mAutoSizePreview);
//        new Handler().postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                mAutoSizePreview.requestLayout();
//            }
//        }, 10000);
//
//        shootBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if(isCapturing||isOCRProceesing){return;}
//                Log.i("check","accessing shootbtn");
//                    isCapturing=true;
//                    isOCRProceesing=true;
//                    shootBtn.setVisibility(View.GONE);
//                    shootBtn.setEnabled(false);
//                    settingBtn.setVisibility(View.GONE);
//                    settingBtn.setEnabled(false);
//                    stopBtn.setVisibility(View.VISIBLE);
//                    stopBtn.setEnabled(true);
//                    stopBtn.setImageResource(R.drawable.baseline_pause_24);
//                    replayBtn.setVisibility(View.VISIBLE);
//                    replayBtn.setEnabled(true);
//                    backBtn.setVisibility(View.VISIBLE);
//                    backBtn.setEnabled(true);
//                    textviewBorder.setVisibility(View.VISIBLE);
//            }
//        });
//        stopBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (true) {
//                    stopBtn.setImageResource(R.drawable.baseline_pause_24);
//                } else {
//                    stopBtn.setImageResource(R.drawable.round_play_arrow_24);
//                }
//            }
//        });
//        replayBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (true) {
//                    stopBtn.setImageResource(R.drawable.baseline_pause_24);
//                } else {
//                    stopBtn.setImageResource(R.drawable.round_play_arrow_24);
//                }
//            }
//        });
//        settingBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                startActivity(new Intent(TestActivity1.this, SettingActivity.class));
//            }
//        });
//        backBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                shootBtn.setVisibility(View.VISIBLE);
//                shootBtn.setEnabled(true);
//                settingBtn.setVisibility(View.VISIBLE);
//                settingBtn.setEnabled(true);
//                stopBtn.setVisibility(View.GONE);
//                stopBtn.setEnabled(false);
//                replayBtn.setVisibility(View.GONE);
//                replayBtn.setEnabled(false);
//                backBtn.setVisibility(View.GONE);
//                backBtn.setEnabled(false);
//                textviewBorder.setVisibility(View.GONE);
//            }
//        });
//
//    }
//
//    protected void onFrameDetect(byte[] frameByte, int width, int height) {
////        Log.d("check", "onPreviewData data="+frameByte.length+",w="+width+",h="+height);
//        if(isCapturing)
//        {
//            Google.performOCR(googleapikey,frameByte,width,height,this);
//            isCapturing=false;
//            Log.d("check","try to save to bitmap once");
//        }
//    }
//
//    @Override
//    protected void onResume() {
//        super.onResume();
//        this.onResumeUsb();
//        // USBMonitor.OnDeviceConnectListener mListener;
//        // mListener.onAttach();
//    }
//
//    @Override
//    protected void onPause() {
//        super.onPause();
//        this.onPauseUsb();
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        this.destroyUsb();
//        this.mHandler.sendEmptyMessageDelayed(7010, 800L);
////                unlock();
//    }
//    public void onTakePhoto(boolean bSucc, String filepath) {
//
//    }
//    protected void destroyFloatView() {
//
//    }
//    @Override
//    public void onBackPressed() {
//        super.onBackPressed();
//
//    }
//
//    @Override
//    public String onRecordStart(int cameraType) {
//        return null;
//    }
//
//    @Override
//    public void onRecordStop(int cameraType, String filePath, String msg) {
//
//    }
//
//    @Override
//    public void onProcessComplete(String text) {
//        Log.i("check return text",text);
//        if (text != null&& !text.isEmpty()) {
//            Log.i("text is ?", text);
//            resultTextView.setText(text);
//        }
//        else{
//            resultTextView.setText("沒有識別到文字，請再次嘗試");
//        }
//    }
//
//    @Override
//    public void onisProcessingChanged(Boolean isProcessing) {
//        this.isOCRProceesing=isProcessing;
//        this.isCapturing=false;
//    }
//    private boolean hasPermissionsGranted(String[] permissions) {
//        for (String permission : permissions) {
//            if (ActivityCompat.checkSelfPermission(getApplicationContext(), permission)
//                    != PackageManager.PERMISSION_GRANTED) {
//                return false;
//            }
//        }
//        return true;
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//    }
//
//    private void restartActivity() {
//        Intent intent = getIntent();
//        finish();
//        startActivity(intent);
//    }
//
//
//}
