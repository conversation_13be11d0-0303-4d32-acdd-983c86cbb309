package com.xy.demo.view;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;

import com.xy.demo.R;
import com.xy.demo.tts.EmbedTTS;
import com.xy.demo.utils.PreferenceManager;

public class SettingActivity extends BaseActivity {
    private Button chineseButton;
    private Button englishButton;
    private Button backButton;
    private Button cantoneseButton;
    private Button mandarinButton;
    private Button spokenengButton;
    private ImageButton slowVoiceSpeedButton;
    private ImageButton fastVoiceSpeedButton;
    private TextView speedTextTV;
    private double selectedSpeed;
    private PreferenceManager preferenceManager;
    private Button[] lanButtonsSet;
    private Button[] voiceLanButtonsSet;
    private String modifiedvoice=null;
    private EmbedTTS embedTTS=null;

//    LanguageManager languageManager;
//    private TTS tts;
//    private boolean needtoupdateresource=false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setting);

        preferenceManager = new PreferenceManager(this);
        embedTTS=new EmbedTTS(this,preferenceManager);
        chineseButton = findViewById(R.id.chineseButton);
        englishButton = findViewById(R.id.englishButton);
        slowVoiceSpeedButton = findViewById(R.id.slowButton);
        fastVoiceSpeedButton = findViewById(R.id.fastButton);
        cantoneseButton = findViewById(R.id.voiceCantoneseButton);
        mandarinButton = findViewById(R.id.voiceMandarinButton);
        spokenengButton=findViewById(R.id.voiceEnglishButton);

        speedTextTV = findViewById(R.id.speedTextTV);
        lanButtonsSet = new Button[]{
                chineseButton,
                englishButton,
        };

        voiceLanButtonsSet = new Button[]{
                cantoneseButton,
                mandarinButton,
                spokenengButton
        };

        String selectedLanguage = preferenceManager.getLanguage();
        selectedSpeed = preferenceManager.getSpeed();
        String selectedVoice = preferenceManager.getVoice();

        Log.i("sharedpre/setting/lan", selectedLanguage);
        Log.i("sharedpre/setting/speed", selectedSpeed + "");
        Log.i("sharedpre/setting/voice", selectedVoice + "");

        setButtonBackground(selectedLanguage, lanButtonsSet);
        setButtonBackground(selectedVoice, voiceLanButtonsSet);
        setSpeedText(selectedSpeed);
        slowVoiceSpeedButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if(selectedSpeed > 0.2) {
                    selectedSpeed = selectedSpeed - 0.1;
                    setSpeedText(selectedSpeed);
                    preferenceManager.saveSpeed((float)selectedSpeed);
                    embedTTS.speakTTS(speedTextTV.getText().toString());
//                    tts.speakTTS(speedTextTV.getText().toString());
//                    needtoupdateresource=true;
//                    Log.i("addspeed/fastVoiceSpeedButton", preferenceManager.getSelectedSpeed() + "");
                }
            }
        });
        fastVoiceSpeedButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if(selectedSpeed < 2.0){
                    selectedSpeed = selectedSpeed + 0.1;
                    setSpeedText(selectedSpeed);
                    preferenceManager.saveSpeed((float)selectedSpeed);
                    embedTTS.speakTTS(speedTextTV.getText().toString());
//                    Log.i("addspeed/fastVoiceSpeedButton", preferenceManager.getSelectedSpeed() + "");
                }
            }
        });
        chineseButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setButtonBackground(chineseButton.getTag().toString(), lanButtonsSet);
                preferenceManager.saveLanguage(PreferenceManager.CHINESE);
                preferenceManager.setLocale(getApplicationContext(),PreferenceManager.CHINESE);
                recreate();
//                Intent intent = new Intent(SettingActivity.this, SplashActivity.class);
//                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//                intent.putExtra("RESTART", true); // Add this line
//                finish();
//                startActivity(intent);
//
            }
        });
        englishButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setButtonBackground(englishButton.getTag().toString(), lanButtonsSet);
                preferenceManager.saveLanguage(PreferenceManager.ENGLISH);
                preferenceManager.setLocale(getApplicationContext(),PreferenceManager.ENGLISH);
                recreate();
//                Intent intent = new Intent(SettingActivity.this, SplashActivity.class);
//                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//                intent.putExtra("RESTART", true); // Add this line
//                finish();
//                startActivity(intent);
            }

        });
        backButton = findViewById(R.id.backButton);
        backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.i("check savedvoice",preferenceManager.getVoice());
                onBackPressed(); // Navigate back to the previous activity
            }
        });
        cantoneseButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                preferenceManager.saveVoice("Cantonese");
                modifiedvoice="Cantonese";
                setButtonBackground(cantoneseButton.getTag().toString(), voiceLanButtonsSet);
            }
        });
        mandarinButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                preferenceManager.saveVoice("Mandarin");
                modifiedvoice="Mandarin";
                setButtonBackground(mandarinButton.getTag().toString(), voiceLanButtonsSet);
//                recreate();
            }
        });
        spokenengButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                preferenceManager.saveVoice("English");
                modifiedvoice="English";
                setButtonBackground(spokenengButton.getTag().toString(), voiceLanButtonsSet);
//                recreate();
            }
        });
    }

    private void setButtonBackground(String selectedValue, Button[] buttonsArray) {
        for (Button button : buttonsArray) {
            if (button != null) {
                String buttonText = button.getTag().toString(); // Get the text of the button
                Log.i("sharedpre/setting3", selectedValue + "  "+buttonText);
                if (buttonText.equals(selectedValue)) {
                    button.setBackgroundResource(R.drawable.selected_button_style);
                } else {
                    button.setBackground(null);
                }
            }
        }
    }

    private void setSpeedText(double selectedSpeed){
        selectedSpeed = selectedSpeed * 10;
        int ShowSpeed = (int) selectedSpeed;
        speedTextTV.setText(" " + ShowSpeed + " ");
    }

    @Override
    public void onBackPressed() {
        // Prepare an intent for returning the result
        Intent resultIntent = new Intent();
        // Assume "defaultVoice" is your default or currently selected voice.
        // You might need to replace this logic with how you actually determine the selected voice.
        // Check if a voice was selected or not
        if (this.modifiedvoice != null) {
            resultIntent.putExtra("voice", this.modifiedvoice);
            setResult(RESULT_OK, resultIntent);
        } else {
            // Optionally, set a different result code to indicate no selection or cancellation
            setResult(RESULT_CANCELED);
        }
        // Call the super method to actually handle the back press
        super.onBackPressed();
    }



//    public boolean updateResourceIfNeeded(){
//        if(this.needtoupdateresource){
//            this.needtoupdateresource=false;
//            return true;
//        }
//        return false;
//    }
}