package com.xy.demo.view;

import static android.content.ContentValues.TAG;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;

import com.xy.demo.utils.PreferenceManager;
import com.xy.demo.utils.VolumeControl;
import com.xy.demo.utils.VolumeObserver;

public class BaseActivity extends AppCompatActivity {

    private PreferenceManager preferenceManager;
    protected String currentLanguage;
    protected String currentVoice;
    protected float currentSpeed;
    private VolumeControl volumeControl;
    private VolumeObserver volumeObserver;
    @Override
    protected void attachBaseContext(Context newBase) {
        preferenceManager=new PreferenceManager(newBase.getApplicationContext());
        if (preferenceManager.isFirstTime()) {
            // Set default values for selectedLanguage, selectedSpeed, and selectedVoice
            preferenceManager.saveLanguage("zh");
            preferenceManager.saveSpeed(1f);
            preferenceManager.saveVoice("Cantonese");
            // Mark that the app has been opened for the first time
            preferenceManager.setFirstTime(false);
        }
        super.attachBaseContext(preferenceManager.onAttach(newBase));
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        preferenceManager = new PreferenceManager(getApplicationContext());
//        volumeControl=new VolumeControl(this);
        if (preferenceManager.isFirstTime()) {
            // Set default values for selectedLanguage, selectedSpeed, and selectedVoice
            preferenceManager.saveLanguage("zh");
            preferenceManager.saveSpeed(1f);
            preferenceManager.saveVoice("Cantonese");
            // Mark that the app has been opened for the first time
            preferenceManager.setFirstTime(false);
        }
        currentLanguage = preferenceManager.getLanguage();
        currentVoice=preferenceManager.getVoice();
        currentSpeed=preferenceManager.getSpeed();
        Log.d(TAG,"current lang "+currentLanguage);
        preferenceManager.setLocale(this, currentLanguage); // Adjust this if setLocale is non-static
        super.onCreate(savedInstanceState);
    }
    @Override
    protected void onResume() {
        super.onResume();
        new Thread(()->{VolumeObserver.changeVolume(this);}).start();
        // Check if the language was changed while the user was away (e.g., in Settings)
        currentVoice=preferenceManager.getVoice();
        currentSpeed=preferenceManager.getSpeed();
        if (!currentLanguage.equals(preferenceManager.getLanguage())) {
           recreate(); // Recreate the activity to reflect language change
        }
        if (volumeObserver == null) {
            volumeObserver = new VolumeObserver(new Handler(Looper.getMainLooper()), this);
        }
        getContentResolver().registerContentObserver(Settings.System.CONTENT_URI, true, volumeObserver);

//        currentLanguage = preferenceManager.getLanguage();
    }
    @Override
    protected void onPause() {
        super.onPause();
        if(volumeObserver!=null){
            getContentResolver().unregisterContentObserver(volumeObserver);
        }
    }

    protected String getCurrentLanguage() {
        return this.currentLanguage;
    }
    protected String getCurrentVoice() {
        return this.currentVoice;
    }
    protected float getCurrentSpeed(){
        return this.currentSpeed;
    }

//    @Override
//    public boolean onKeyUp(int keyCode, KeyEvent event) {
//        if ((keyCode == KeyEvent.KEYCODE_VOLUME_UP)) {
//            // Your logic here, for example:
//            Log.d("VolumeControl", "Volume button pressed");
//            if(volumeControl==null){return true;}
//            if(volumeControl.enforceVolumeLimit()){
//                return true;
//            }
//            else{
//                return super.onKeyUp(keyCode, event);
//            }
//            // Optionally, adjust the volume yourself and return true to indicate you've handled the event
//        }
//      return true;
//    }


}