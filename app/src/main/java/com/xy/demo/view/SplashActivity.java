package com.xy.demo.view;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.core.content.ContextCompat;

import com.xy.demo.R;
import com.xy.demo.databinding.ActivitySplashBinding;

import java.util.ArrayList;
import java.util.List;

public class SplashActivity extends BaseActivity {

    private ActivitySplashBinding binding;
    private boolean isWaitingForPermissionResult = false;
    private boolean isInitialLaunch = true;

    // --- 用於處理權限請求結果的 Launchers ---

    // 用於處理標準執行時權限 (相機、錄音等) 的 Launcher
    private final ActivityResultLauncher<String[]> requestRuntimePermissionsLauncher =
            registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(), results -> {
                isWaitingForPermissionResult = false;
                // 重新檢查實際權限狀態，而不是僅依賴結果回調
                if (!hasRuntimePermissions()) {
                    Toast.makeText(this, getString(R.string.permission_camera_audio_required), Toast.LENGTH_LONG).show();
                } else {
                    // 所有標準權限都已授予，繼續檢查下一個特殊權限
                    checkSpecialPermissions();
                }
            });

    // 用於處理「所有檔案存取權」的 Launcher
    private final ActivityResultLauncher<Intent> manageStoragePermissionLauncher =
            registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
                isWaitingForPermissionResult = false;
                // 從設定頁面返回後，繼續檢查權限流程
                checkSpecialPermissions();
            });

    // 用於處理「懸浮窗權限」的 Launcher
    private final ActivityResultLauncher<Intent> overlayPermissionLauncher =
            registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
                isWaitingForPermissionResult = false;
                // 從設定頁面返回後，繼續檢查權限流程
                checkSpecialPermissions();
            });


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySplashBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        binding.singlecamera.setOnClickListener(v -> {
            // 使用者點擊按鈕，開始完整的權限檢查流程
            checkAndRequestPermissions();
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 只有在不是初次啟動且沒有等待權限結果時才檢查權限
        if (!isInitialLaunch && !isWaitingForPermissionResult) {
            checkAndRequestPermissions();
        }
        isInitialLaunch = false;
    }

    /**
     * 這是權限檢查流程的總入口。
     * 它會依序檢查所有必要的權限。
     */
    private void checkAndRequestPermissions() {
        // 如果正在等待權限結果，直接返回
        if (isWaitingForPermissionResult) {
            return;
        }
        
        // 步驟 1: 檢查標準執行時權限
        if (!hasRuntimePermissions()) {
            isWaitingForPermissionResult = true;
            requestRuntimePermissionsLauncher.launch(getRuntimePermissionsToRequest());
            return; // 在此停止，等待使用者回應
        }

        // 步驟 2: 檢查特殊權限 (儲存和懸浮窗)
        checkSpecialPermissions();
    }

    /**
     * 在標準權限被授予後，檢查特殊權限。
     */
    private void checkSpecialPermissions() {
        // 在 Android 11+ 檢查「所有檔案存取權」
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && !Environment.isExternalStorageManager()) {
            Toast.makeText(this, getString(R.string.permission_storage_next), Toast.LENGTH_SHORT).show();
            isWaitingForPermissionResult = true;
            Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
            intent.setData(Uri.parse("package:" + getPackageName()));
            manageStoragePermissionLauncher.launch(intent);
            return; // 在此停止，等待使用者從設定頁返回
        }

        // 在 Android 6+ 檢查「懸浮窗權限」
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Toast.makeText(this, getString(R.string.permission_overlay_next), Toast.LENGTH_SHORT).show();
            isWaitingForPermissionResult = true;
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + getPackageName()));
            overlayPermissionLauncher.launch(intent);
            return; // 在此停止，等待使用者從設定頁返回
        }

        // 如果程式能執行到這裡，代表所有權限都已被授予
        navigateToTestActivity();
    }

    /**
     * 輔助方法，檢查是否擁有所有必要的「標準」執行時權限。
     */
    private boolean hasRuntimePermissions() {
        for (String permission : getRuntimePermissionsToRequest()) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根據 SDK 版本動態決定需要請求的執行時權限列表。
     */
    private String[] getRuntimePermissionsToRequest() {
        List<String> permissions = new ArrayList<>();
        permissions.add(Manifest.permission.CAMERA);
        permissions.add(Manifest.permission.RECORD_AUDIO);

        // 儲存權限只在 Android 10 (API 29) 及以下版本需要請求。
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        return permissions.toArray(new String[0]);
    }

    private void navigateToTestActivity() {
        // 防止在 Activity 即將銷毀時重複啟動
        if (isFinishing() || isDestroyed()) {
            return;
        }
        Intent intent = new Intent(SplashActivity.this, TestActivity.class);
        startActivity(intent);
        finish();
    }
}
