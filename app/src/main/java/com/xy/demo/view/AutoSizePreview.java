package com.xy.demo.view;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.TextureView;
import android.view.View;

public class AutoSizePreview extends TextureView {
    private static String TAG = AutoSizePreview.class.getSimpleName();
    private int mRatioWidth;
    private int mRatioHeight;

    public AutoSizePreview(Context var1) {
        this(var1, (AttributeSet)null);
    }

    public AutoSizePreview(Context var1, AttributeSet var2) {
        this(var1, var2, 0);
    }

    public AutoSizePreview(Context var1, AttributeSet var2, int var3) {
        super(var1, var2, var3);
        this.mRatioWidth = 0;
        this.mRatioHeight = 0;
    }

    public void setAspectRatio(int var1, int var2) {
        Log.v(TAG, "--- setAspectRatio();width: " + var1 + ";height: " + var2);
        if (var1 >= 0 && var2 >= 0) {
            this.mRatioWidth = var1;
            this.mRatioHeight = var2;
            this.requestLayout();
        } else {
            throw new IllegalArgumentException("Size cannot be negative.");
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        //
        int width = View.MeasureSpec.getSize(widthMeasureSpec);
        int height = View.MeasureSpec.getSize(heightMeasureSpec);

        //
        setMeasuredDimension(height, width);
//        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
//        int width = View.MeasureSpec.getSize(widthMeasureSpec);
//        int height = View.MeasureSpec.getSize(heightMeasureSpec);
//        if (mRatioWidth == 0 || mRatioHeight == 0) {
//            setMeasuredDimension(width, height);
//        } else {
//            // Adjust the width and height to maintain the aspect ratio
//            float ratio = mRatioWidth / (float) mRatioHeight;
//            float viewRatio = width / (float) height;
//            if (viewRatio > ratio) {
//                width = (int) (height * ratio);
//            } else {
//                height = (int) (width / ratio);
//            }
//            setMeasuredDimension(width, height);
//        }

    }
}

