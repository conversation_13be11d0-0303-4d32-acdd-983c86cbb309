//package com.xy.demo.utils;
//
//import android.content.BroadcastReceiver;
//import android.content.Context;
//import android.content.Intent;
//import android.media.AudioManager;
//import android.util.Log;
//import android.widget.Toast;
//
//public class VolumeChangeReceiver extends BroadcastReceiver {
//    @Override
//    public void onReceive(Context context, Intent intent) {
//        String intentAction = intent.getAction();
//        Toast.makeText(context, "debug media button test", Toast.LENGTH_SHORT).show();
//        if ("android.media.VOLUME_CHANGED_ACTION".equals(intent.getAction())) {
//            Log.i("check volume","volume changed");
//            VolumeControl volumeControl = new VolumeControl(context);
//            volumeControl.enforceVolumeLimit();
//        }
//    }
//
//}
//
//
//
