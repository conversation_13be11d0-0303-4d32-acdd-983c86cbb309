package com.xy.demo.utils;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;

public class Dialog {
    private static String title,message,confirm;
    public interface DialogCallback {
//        void onPositiveButtonClicked();
    }
    public static void showDialog(Context context,DialogCallback callback,String language) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        mapLanguage(language);
        builder.setTitle(title);
        builder.setMessage(message);
        // Confirm Button
        builder.setPositiveButton(confirm, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
//                Toast.makeText(context, "Confirmed", Toast.LENGTH_SHORT).show();
//                if (callback != null) {
//                    callback.onPositiveButtonClicked();
//                }
                dialog.dismiss();
            }
        });
        // Cancel Button
//        builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
//            @Override
//            public void onClick(DialogInterface dialog, int which) {
////                Toast.makeText(context, "Canceled", Toast.LENGTH_SHORT).show();
//                if (callback != null) {
//                    callback.onNegativeButtonClicked();
//                }
//                dialog.dismiss();
//            }
//        });

        builder.setCancelable(false);

        android.app.Dialog dialog = builder.create();
        dialog.show();
    }
    public static void mapLanguage(String language){
        if(language.equals(PreferenceManager.CHINESE)){
          title="溫馨提示";
          message="為保護你的聽力以及保證程序正常運作，請將無障礙功能聲量、媒體音量、提示音量調整至百分之七十以下";
          confirm="確認";
        }
        else{
            title="Warm Reminder";
            message="To protect your hearing and ensure the program operates normally, please adjust the accessibility volume, media volume, and notification volume to below 70 percent.";
            confirm="Confirm";
        }
    }
}