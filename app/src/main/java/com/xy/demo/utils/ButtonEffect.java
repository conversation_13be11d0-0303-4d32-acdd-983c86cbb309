package com.xy.demo.utils;
import com.xy.demo.R;
//import android.annotation.SuppressLint;
//import android.content.Context;
//import android.media.AudioFormat;
//import android.media.AudioManager;
//import android.media.AudioTrack;
//

//
//import java.io.IOException;
//import java.io.InputStream;
//
//public class ButtonEffect {
//    private Context context;
//    private int bufferSize;
//    private AudioTrack audioTrack;
//    private InputStream is;
//
//    private void init(){
//        this.bufferSize = AudioTrack.getMinBufferSize(44100,
//                AudioFormat.CHANNEL_OUT_STEREO,
//                AudioFormat.ENCODING_PCM_16BIT);
//        this.audioTrack = new AudioTrack(AudioManager.STREAM_MUSIC,
//                44100,
//                AudioFormat.CHANNEL_OUT_STEREO,
//                AudioFormat.ENCODING_PCM_16BIT,
//                bufferSize,
//                AudioTrack.MODE_STREAM);
//    }
//    public ButtonEffect(Context context) {
//        this.context = context;
//        this.init();
//    }
//    public synchronized void play() {
//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//                prepare();
//                is = context.getResources().openRawResource(R.raw.clickeffect);
//                byte[] audioData = new byte[bufferSize];
//
//                audioTrack.play();
//                try {
//                    int read;
//                    while ((read = is.read(audioData)) != -1) {
//                        audioTrack.write(audioData, 0, read);
//                    }
//                } catch (IOException e) {
//                    e.printStackTrace();
//                } finally {
//                    try {
//                        is.close();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                    audioTrack.stop();
//                    audioTrack.flush();
//                }
//            }
//        }).start();
//    }
//    private synchronized void prepare() {
//        if (audioTrack == null || audioTrack.getState() != AudioTrack.STATE_INITIALIZED) {
//            this.init(); // Re-initialize if AudioTrack is not ready
//        } else if (audioTrack.getPlayState() != AudioTrack.PLAYSTATE_STOPPED) {
//            audioTrack.stop(); // Stop playback if currently playing or paused
//            audioTrack.flush(); // Clear the buffer
//        }
//        if (is != null) {
//            try {
//                is.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//    public void release() {
//        if (audioTrack != null) {
//            audioTrack.release();
//            audioTrack = null;
//        }
//    }
//}


import android.content.Context;
import android.media.AudioAttributes;
import android.media.SoundPool;

public class ButtonEffect {
    private SoundPool soundPool;
    private int soundId;

    public ButtonEffect(Context context) {
        // Initialize SoundPool
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_GAME)
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .build();

        soundPool = new SoundPool.Builder()
                .setMaxStreams(1)
                .setAudioAttributes(audioAttributes)
                .build();

        // Load the sound effect
        soundId = soundPool.load(context, R.raw.clickeffect, 1);
    }

    public void play() {
        // Play the sound effect
        this.stop();
        soundPool.play(soundId, 1, 1, 0, 0, 1);
    }
    public void stop(){
        soundPool.stop(soundId);
    }

    public void release() {
        // Release SoundPool resources
        soundPool.release();
    }
}