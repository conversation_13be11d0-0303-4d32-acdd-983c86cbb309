package com.xy.demo.utils;

import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

import androidx.annotation.NonNull;

public class VolumeControl {
    private AudioManager audioManager;

    public VolumeControl(@NonNull Context context) {
        audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
    }

    public void setVolumeLimit() {
        // Get the maximum volume level for music or multimedia.
        int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        // Calculate 70% of the max volume.
        int seventyPercentMax = (int) (maxVolume * 0.7);
        // Set the current volume to 70% of the maximum volume.
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, seventyPercentMax, 0);
    }

    // Call this method to reset the volume if it exceeds 70%
    public boolean enforceVolumeLimit() {
        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        int seventyPercentMax = (int) (maxVolume * 0.2);
        if (currentVolume > seventyPercentMax) {
            Log.i("check volume","set to threshold");
            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, seventyPercentMax, 0);
            return true;
        }
        return false;
    }
}
