//package com.xy.demo.utils;
//
//import android.Manifest;
//import android.app.Activity;
//import android.content.Context;
//import android.content.pm.PackageManager;
//import android.graphics.ImageFormat;
//import android.graphics.SurfaceTexture;
//import android.hardware.camera2.CameraAccessException;
//import android.hardware.camera2.CameraCaptureSession;
//import android.hardware.camera2.CameraCharacteristics;
//import android.hardware.camera2.CameraDevice;
//import android.hardware.camera2.CameraManager;
//import android.hardware.camera2.CaptureRequest;
//import android.hardware.camera2.TotalCaptureResult;
//import android.media.Image;
//import android.media.ImageReader;
//import android.os.Environment;
//import android.util.Log;
//import android.view.Surface;
//import android.view.TextureView;
//
//import androidx.annotation.NonNull;
//import androidx.core.app.ActivityCompat;
//
//import com.xy.usb.api.MyTextureView;
//
//import java.io.File;
//import java.io.FileNotFoundException;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.nio.ByteBuffer;
//import java.util.Arrays;
//import com.xy.demo.view.TestActivity;
//
//public class BackCam {
//    private Context context;
//    private MyTextureView mTextureView = null;
//    private int mPreviewWidth = 1280;
//    private int mPreviewHeight = 720;
//    private CameraDevice cameraDevice;
//    private CameraCaptureSession cameraCaptureSession;
//    private boolean isOpened=false;
//
//    private ImageReader imageReader;
//    private final TestActivity testActivity; // 用於回呼
//
//    public BackCam(Context context,MyTextureView mTextureView){
//        this.context=context;
//        this.mTextureView=mTextureView;
//        this.testActivity = (TestActivity) context;
//        openCam();
//    }
//    public void switchToBackCamera() {
//        CameraManager manager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
//        try {
//            assert manager != null;
//            for (String cameraId : manager.getCameraIdList()) {
//                CameraCharacteristics characteristics = manager.getCameraCharacteristics(cameraId);
//                Integer facing = characteristics.get(CameraCharacteristics.LENS_FACING);
//                if (facing != null && facing == CameraCharacteristics.LENS_FACING_BACK) {
//                    if (ActivityCompat.checkSelfPermission(this.context, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
//                        //TODO
//                        // Request camera permissions here if not already granted
//                        if (ActivityCompat.shouldShowRequestPermissionRationale((Activity) context, Manifest.permission.CAMERA)) {
//                            // Provide an additional rationale to the user if the permission was not granted
//                            // and the user would benefit from additional context for the use of the permission.
//                            // Display a dialog or a snackbar explaining why the permission is needed, then
//                            // request the permission again.
//                            // ...
//                            return;
//                        } else {
//                            // Camera permission has not been granted yet. Request it directly.
//                            ActivityCompat.requestPermissions((Activity) context, new String[]{Manifest.permission.CAMERA}, 1);
//                        }
//                        return;
//                    }
//                    manager.openCamera(cameraId, new CameraDevice.StateCallback() {
//                        @Override
//                        public void onOpened(@NonNull CameraDevice camera) {
//                            // Use the camera for preview or take a picture
//                            cameraDevice = camera;
//                            startCameraPreview(camera);
//                            isOpened=true;
//                        }
//
//                        @Override
//                        public void onDisconnected(@NonNull CameraDevice camera) {
////                            camera.close();
////                            if (cameraDevice != null && cameraDevice.equals(camera)) {
////                                cameraDevice = null;
////                            }
//                            closeCam();
//                            isOpened=false;
//                        }
//
//                        @Override
//                        public void onError(@NonNull CameraDevice camera, int error) {
////                            camera.close();
////                            if (cameraDevice != null && cameraDevice.equals(camera)) {
////                                cameraDevice = null;
////                            }
//                            closeCam();
//                            isOpened=false;
//                        }
//                    }, null);
//                    break; // Break out of the loop once the back camera is found and opened
//                }
//            }
//        } catch (CameraAccessException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void startCameraPreview(CameraDevice cameraDevice) {
//        // Here, you need to set up the Surface for the camera preview and start the preview session.
//        // This typically involves creating a CaptureRequest.Builder with TEMPLATE_PREVIEW,
//        // setting up a Surface as the target for this request, and finally,
//        // creating a CameraCaptureSession to start the preview.
//        try {
//            SurfaceTexture texture = mTextureView.getSurfaceTexture();
//            assert texture != null;
////            texture.setDefaultBufferSize(previewSize.getWidth(), previewSize.getHeight());
//            texture.setDefaultBufferSize(mPreviewWidth,mPreviewHeight);
//            Surface surface = new Surface(texture);
//            final CaptureRequest.Builder builder = cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
//            builder.addTarget(surface);
//
//            imageReader = ImageReader.newInstance(mPreviewWidth, mPreviewHeight, ImageFormat.JPEG, 2);
//            Surface imageReaderSurface = imageReader.getSurface();
//
//
//            cameraDevice.createCaptureSession(Arrays.asList(surface, imageReaderSurface),
//                    new CameraCaptureSession.StateCallback() {
//                        @Override
//                        public void onConfigured(@NonNull CameraCaptureSession session) {
//                            cameraCaptureSession = session;
//                            try {
//                                session.setRepeatingRequest(builder.build(), null, null);
//                            } catch (CameraAccessException e) {
//                                Log.e("Camera", "Error setting up preview", e);
//                            }
//                        }
//
//                        @Override
//                        public void onConfigureFailed(@NonNull CameraCaptureSession session) {
//                            Log.e("Camera", "Configuration failed");
//                        }
//                    }, null);
//        } catch (CameraAccessException e) {
//            Log.e("Camera", "Access exception during camera setup", e);
//        }
//
//        // Handle the captured image
//        imageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {
//            @Override
//            public void onImageAvailable(ImageReader reader) {
//                Image image = null;
//                FileOutputStream fos = null;
//                ByteBuffer buffer = null;
//                byte[] bytes;
//                try {
//                    image = reader.acquireLatestImage();
//                    if (image != null) {
//                        buffer = image.getPlanes()[0].getBuffer();
//                        bytes = new byte[buffer.remaining()];
//                        buffer.get(bytes); // Read the image data
//
//                        // Save the image to a file
//                        File file = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "myPhoto_" + System.currentTimeMillis() + ".jpg");
//                        Log.i("check", (Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)+ "/myPhoto_" + System.currentTimeMillis() + ".jpg"));
//                        fos = new FileOutputStream(file);
//                        fos.write(bytes);
//                    }
//                } catch (IOException e) {
//                    Log.e("Camera", "Error saving image", e);
//                } finally {
//                    if (buffer != null) {
//                        buffer.clear();
//                    }
//                    if (fos != null) {
//                        try {
//                            fos.close();
//                        } catch (IOException e) {
//                            Log.e("Camera", "Error closing file output stream", e);
//                        }
//                    }
//                    if (image != null) {
//                        image.close();
//                    }
//
//                }
//            }
//        }, null);
//
//
//
//    }
//
//    public final TextureView.SurfaceTextureListener mSurfaceTextureListener = new TextureView.SurfaceTextureListener() {
//        @Override
//        public void onSurfaceTextureAvailable(@NonNull SurfaceTexture surface, int width, int height) {
//            // Open your camera here
//            switchToBackCamera();
//        }
//
//        @Override
//        public void onSurfaceTextureSizeChanged(@NonNull SurfaceTexture surface, int width, int height) {
//            // Handle size changes if necessary
//            Log.i("check","onSurfaceTextureSizeChanged");
//        }
//
//        @Override
//        public boolean onSurfaceTextureDestroyed(@NonNull SurfaceTexture surface) {
//            return true;
//        }
//
//        @Override
//        public void onSurfaceTextureUpdated(@NonNull SurfaceTexture surface) {
//            // Update your view if necessary
////            Log.i("check","onSurfaceTextureUpdated");
//        }
//    };
//
//    public void closeCam(){
//        mTextureView.setSurfaceTextureListener(null);
//        if (cameraCaptureSession != null) {
//            cameraCaptureSession.close();
//            cameraCaptureSession = null;
//        }
//
//        if (cameraDevice != null) {
//            cameraDevice.close();
//            cameraDevice = null;
//        }
//    }
//
//    public void openCam(){
//        if(isOpened){
//            return;
//        }
//        if(mTextureView.isAvailable()){
//            this.switchToBackCamera();
//        }else{
//            Log.i("check","switching to backcam");
//            mTextureView.setSurfaceTextureListener(this.mSurfaceTextureListener);
//        }
//    }
//
//    public void capturePhoto() {
//        if (cameraDevice == null) return;
//        try {
//            final CaptureRequest.Builder captureBuilder = cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE);
//            captureBuilder.addTarget(imageReader.getSurface());
//            // Optional: Add some capture metadata like auto-focus, auto-exposure, etc.
//            captureBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
//
//            // Capture the photo
//            cameraCaptureSession.capture(captureBuilder.build(), new CameraCaptureSession.CaptureCallback() {
//                @Override
//                public void onCaptureCompleted(@NonNull CameraCaptureSession session, @NonNull CaptureRequest request, @NonNull TotalCaptureResult result) {
//                    super.onCaptureCompleted(session, request, result);
//                    // Optionally, process the capture result or notify the user
//                }
//            }, null);
//        } catch (CameraAccessException e) {
//            e.printStackTrace();
//        }
//    }
//}
//


package com.xy.demo.utils;

import android.content.Context;
import android.graphics.ImageFormat;
import android.util.Log;
import android.view.TextureView;
import androidx.annotation.NonNull;
import androidx.annotation.OptIn;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ExperimentalGetImage;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.core.ImageProxy;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleOwner;
import com.google.common.util.concurrent.ListenableFuture;
import com.xy.demo.view.TestActivity;

import java.lang.ref.WeakReference;
import java.nio.ByteBuffer;
import java.util.concurrent.ExecutionException;

public class BackCam {
    private final Context context;
    private final PreviewView cameraView;
    private ImageCapture imageCapture;
    private ProcessCameraProvider cameraProvider;
    // 使用WeakReference防止記憶體洩漏
    private final WeakReference<TestActivity> testActivityRef;

    public BackCam(Context context, PreviewView cameraView) {
        this.context = context;
        this.cameraView = cameraView;
        
        // 使用WeakReference包裝TestActivity以防止記憶體洩漏
        if (context instanceof TestActivity) {
            this.testActivityRef = new WeakReference<>((TestActivity) context);
        } else {
            this.testActivityRef = new WeakReference<>(null);
            Log.w("BackCam", "Context is not TestActivity instance");
        }
        
        openCam();
    }
    
    /**
     * 安全地獲取TestActivity引用
     * @return TestActivity實例，如果已被回收則返回null
     */
    private TestActivity getTestActivity() {
        return testActivityRef.get();
    }

    public void openCam() {
        ListenableFuture<ProcessCameraProvider> cameraProviderFuture = ProcessCameraProvider.getInstance(context);
        cameraProviderFuture.addListener(() -> {
            try {
                cameraProvider = cameraProviderFuture.get();
                bindPreview(cameraProvider);
            } catch (ExecutionException | InterruptedException e) {
                Log.e("BackCam", "Error opening camera", e);
            }
        }, ContextCompat.getMainExecutor(context));
    }

    private void bindPreview(@NonNull ProcessCameraProvider cameraProvider) {
        cameraProvider.unbindAll();
        Preview preview = new Preview.Builder().build();
        CameraSelector cameraSelector = new CameraSelector.Builder()
                .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                .build();

        // 設定 Preview 的畫面輸出
        // 注意：這裡的 TextureView 應該是 CameraX 的 PreviewView，為了相容舊程式碼暫時保留
        // preview.setSurfaceProvider(((androidx.camera.view.PreviewView) textureView).getSurfaceProvider());
        preview.setSurfaceProvider(cameraView.getSurfaceProvider());
        imageCapture = new ImageCapture.Builder()
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                .build();

        cameraProvider.bindToLifecycle((LifecycleOwner) context, cameraSelector, preview, imageCapture);
    }

    /**
     * 優化的照片拍攝方法，包含完整的記憶體管理和錯誤處理
     */
    public void capturePhoto() {
        if (imageCapture == null) {
            Log.e("BackCam", "ImageCapture is not initialized.");
            TestActivity activity = getTestActivity();
            if (activity != null) {
                activity.onOcrError("Camera not ready");
            }
            return;
        }

        Log.d("BackCam", "Starting optimized photo capture");
        
        // 使用優化的ImageCapture配置和回調處理
        imageCapture.takePicture(ContextCompat.getMainExecutor(context), 
            new OptimizedImageCapturedCallback());
    }
    
    /**
     * 優化的圖像捕獲回調，包含完整的記憶體管理
     */
    private class OptimizedImageCapturedCallback extends ImageCapture.OnImageCapturedCallback {
        private final long captureStartTime = System.currentTimeMillis();
        
        @Override
        public void onCaptureSuccess(@NonNull ImageProxy image) {
            long processingStartTime = System.currentTimeMillis();
            Log.d("BackCam", String.format("Photo capture success in %d ms", 
                processingStartTime - captureStartTime));
            
            // 使用try-with-resources確保ImageProxy正確關閉
            try {
                // 驗證ImageProxy狀態
                if (!validateImageProxy(image)) {
                    return;
                }
                
                // 記錄記憶體使用情況
                logMemoryUsage("Before processing");
                
                // 處理圖像（將ImageProxy傳遞給OCR處理）
                processImageSafely(image);
                
                // 記錄處理完成時間
                long processingTime = System.currentTimeMillis() - processingStartTime;
                Log.d("BackCam", String.format("Image processing completed in %d ms", processingTime));
                
            } catch (Exception e) {
                Log.e("BackCam", "Error during image processing", e);
                TestActivity activity = getTestActivity();
                if (activity != null) {
                    activity.onOcrError("Image processing failed: " + e.getMessage());
                }
            } finally {
                // 確保ImageProxy被正確關閉，即使發生異常
                safeCloseImageProxy(image);
                
                // 記錄處理後的記憶體使用情況
                logMemoryUsage("After processing");
                
                // 建議垃圾回收以釋放記憶體
                suggestGarbageCollection();
            }
        }

        @Override
        public void onError(@NonNull ImageCaptureException exception) {
            long captureTime = System.currentTimeMillis() - captureStartTime;
            Log.e("BackCam", String.format("Photo capture failed after %d ms: %s", 
                captureTime, exception.getMessage()), exception);
            
            TestActivity activity = getTestActivity();
            if (activity != null) {
                String errorMessage = "Camera capture failed";
                
                // 根據錯誤類型提供更具體的錯誤信息
                switch (exception.getImageCaptureError()) {
                    case ImageCapture.ERROR_CAMERA_CLOSED:
                        errorMessage = "Camera was closed";
                        break;
                    case ImageCapture.ERROR_CAPTURE_FAILED:
                        errorMessage = "Photo capture failed";
                        break;
                    case ImageCapture.ERROR_FILE_IO:
                        errorMessage = "File I/O error";
                        break;
                    default:
                        errorMessage = "Unknown camera error";
                        break;
                }
                
                activity.onOcrError(errorMessage);
            }
        }
    }
    
    /**
     * 驗證ImageProxy的有效性
     */
    @OptIn(markerClass = ExperimentalGetImage.class)
    private boolean validateImageProxy(@NonNull ImageProxy image) {
        try {
            if (image.getImage() == null) {
                Log.e("BackCam", "ImageProxy contains null Image");
                return false;
            }
            
            if (image.getWidth() <= 0 || image.getHeight() <= 0) {
                Log.e("BackCam", String.format("Invalid image dimensions: %dx%d", 
                    image.getWidth(), image.getHeight()));
                return false;
            }
            
            // 檢查圖像格式
            int format = image.getFormat();
            if (format != ImageFormat.YUV_420_888 && format != ImageFormat.JPEG) {
                Log.w("BackCam", String.format("Unexpected image format: %d", format));
            }
            
            Log.d("BackCam", String.format("Image validated: %dx%d, format: %d", 
                image.getWidth(), image.getHeight(), format));
            
            return true;
            
        } catch (Exception e) {
            Log.e("BackCam", "Error validating ImageProxy", e);
            return false;
        }
    }
    
    /**
     * 安全地處理圖像，包含超時機制
     */
    @OptIn(markerClass = ExperimentalGetImage.class)
    private void processImageSafely(@NonNull ImageProxy image) {
        TestActivity activity = getTestActivity();
        if (activity == null) {
            Log.w("BackCam", "TestActivity reference is null or has been garbage collected, cannot process image");
            return;
        }
        
        try {
            // 檢查圖像是否仍然有效
            if (image.getImage() == null) {
                Log.e("BackCam", "ImageProxy became invalid before processing");
                return;
            }
            
            // 將圖像傳遞給OCR處理
            // 注意：不在這裡關閉ImageProxy，由OfflineOCR負責關閉
            activity.processBackCameraImage(image);
            
        } catch (Exception e) {
            Log.e("BackCam", "Error in processImageSafely", e);
            throw e; // 重新拋出異常，由調用者處理
        }
    }
    
    /**
     * 安全地關閉ImageProxy，防止重複關閉
     */
    @OptIn(markerClass = ExperimentalGetImage.class)
    private void safeCloseImageProxy(@NonNull ImageProxy image) {
        try {
            // 檢查ImageProxy是否已經關閉
            if (image.getImage() != null) {
                image.close();
                Log.d("BackCam", "ImageProxy closed successfully");
            } else {
                Log.d("BackCam", "ImageProxy was already closed");
            }
        } catch (Exception e) {
            Log.e("BackCam", "Error closing ImageProxy", e);
        }
    }
    
    /**
     * 記錄記憶體使用情況
     */
    private void logMemoryUsage(String stage) {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            double usedPercentage = (double) usedMemory / maxMemory * 100;
            
            Log.d("BackCam", String.format("Memory usage %s: %.1f%% (%d MB / %d MB)", 
                stage, usedPercentage, usedMemory / (1024 * 1024), maxMemory / (1024 * 1024)));
            
            // 警告記憶體使用率過高
            if (usedPercentage > 80) {
                Log.w("BackCam", "High memory usage detected: " + usedPercentage + "%");
            }
            
        } catch (Exception e) {
            Log.e("BackCam", "Error logging memory usage", e);
        }
    }
    
    /**
     * 建議進行垃圾回收
     */
    private void suggestGarbageCollection() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
            
            // 建議垃圾回收
            System.gc();
            
            // 等待一小段時間讓GC完成
            Thread.sleep(50);
            
            long afterMemory = runtime.totalMemory() - runtime.freeMemory();
            long freedMemory = beforeMemory - afterMemory;
            
            if (freedMemory > 0) {
                Log.d("BackCam", String.format("GC freed %d MB of memory", 
                    freedMemory / (1024 * 1024)));
            }
            
        } catch (Exception e) {
            Log.e("BackCam", "Error during garbage collection", e);
        }
    }

    public void closeCam() {
        if (cameraProvider != null) {
            try {
                cameraProvider.unbindAll();
                // 確保相機資源完全釋放
                imageCapture = null;
            } catch (Exception e) {
                Log.e("BackCam", "Error closing camera", e);
            }
        }
    }

    public void switchToBackCamera() {
        openCam();
    }

    // 省略 mSurfaceTextureListener，因為 CameraX 使用不同的機制
}
