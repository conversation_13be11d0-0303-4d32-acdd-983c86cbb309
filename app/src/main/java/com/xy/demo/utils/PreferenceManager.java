package com.xy.demo.utils;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.Log;

import java.util.Locale;

public class PreferenceManager {

    private static final String SELECTED_LANGUAGE = "LocaleHelper.selectedLanguage";
    private static final String PREFERENCES_NAME = "MyAppPreferences";
    private static final String LANGUAGE_KEY = "selectedLanguage";
    private static final String SPEED_KEY = "selectedSpeed";
    private static final String VOICE_KEY = "selectVoice";
    private static final String PREF_FIRST_TIME = "first_time";
    public static final String ENGLISH = "en";
    public static final String CHINESE = "zh";
    private final SharedPreferences preferences;
    private final SharedPreferences.Editor editor;

    public PreferenceManager(Context context) {
        preferences = context.getSharedPreferences(PREFERENCES_NAME, Context.MODE_PRIVATE);
        editor = preferences.edit();
    }

    public String getLanguage() {
        Log.i("getSelectedLanguage", preferences.getString(LANGUAGE_KEY, ""));
        return preferences.getString(LANGUAGE_KEY, ENGLISH);
    }

    public void saveLanguage(String language) {
        editor.putString(LANGUAGE_KEY, language);
        editor.apply();
    }

    public float getSpeed() {
        return preferences.getFloat(SPEED_KEY, 1f);
    }

    public void saveSpeed(Float speed) {
        editor.putFloat(SPEED_KEY, speed);
        editor.apply();
//        Log.i("getSelectedSpeed",preferences.getFloat(SPEED_KEY, 1f)+"");
    }

    public void saveVoice(String voice) {
        editor.putString(VOICE_KEY, voice);
        editor.apply();
    }

    public String getVoice(){
        return preferences.getString(VOICE_KEY, "Cantonese");
    }

    public boolean isFirstTime() {
        return preferences.getBoolean(PREF_FIRST_TIME, true);
    }

    public  void setFirstTime(boolean isFirstTime) {
        editor.putBoolean(PREF_FIRST_TIME, isFirstTime);
        editor.commit();
    }

    public  Context onAttach(Context context) {
        String lang=getLanguage();
        return setLocale(context, lang);
    }

    public Context setLocale(Context context, String language) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return updateResources(context, language);
        }
        return updateResourcesLegacy(context, language);
    }

    @TargetApi(Build.VERSION_CODES.N)
    private static Context updateResources(Context context, String language) {
        Locale locale = new Locale(language);

        Locale.setDefault(locale);
        Configuration config = context.getResources().getConfiguration();
        config.setLocale(locale);
        config.setLayoutDirection(locale);

        return context.createConfigurationContext(config);
    }

    @SuppressWarnings("deprecation")
    private static Context updateResourcesLegacy(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        Resources resources = context.getResources();

        Configuration config = resources.getConfiguration();
        config.locale = locale;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            config.setLayoutDirection(locale);
        }
        resources.updateConfiguration(config, resources.getDisplayMetrics());
        return context;
    }
}
