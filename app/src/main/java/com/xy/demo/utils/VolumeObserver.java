package com.xy.demo.utils;

import android.content.Context;
import android.database.ContentObserver;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Handler;
import android.util.Log;

import androidx.annotation.NonNull;

public class VolumeObserver extends ContentObserver {
    private Context context;

    public VolumeObserver(Handler handler, Context context) {
        super(handler);
        this.context = context;
    }

    @Override
    public void onChange(boolean selfChange, Uri uri) {
        super.onChange(selfChange, uri);
//        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
//        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
//        Log.i("check","volume changed "+currentVolume);
        VolumeObserver.changeVolume(this.context);
        // Adjust volume if necessary
        // For example, resetting to a specific level or applying custom logic
    }


    public static void changeVolume(@NonNull Context context){
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        // For media volume
        int[] streams = new int[] {AudioManager.STREAM_MUSIC, AudioManager.STREAM_NOTIFICATION, AudioManager.STREAM_ALARM};
        for (int stream : streams) {
            int maxVolume = audioManager.getStreamMaxVolume(stream);
            int desiredVolume = (int) (maxVolume * 0.6); // 70% of max volume
            int currentVolume = audioManager.getStreamVolume(stream);
            // Only adjust if the current volume is higher than the desired volume.
            if (currentVolume > desiredVolume) {
                audioManager.setStreamVolume(stream, desiredVolume, AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE);
            }
        }
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            audioManager.adjustVolume(AudioManager.STREAM_ACCESSIBILITY ,AudioManager.ADJUST_LOWER);
//            audioManager.setStreamVolume(AudioManager.STREAM_ACCESSIBILITY,2, 0);
//            Log.i("check","current accessibility volume "+audioManager.getStreamVolume(AudioManager.STREAM_ACCESSIBILITY));
//            Log.i("check","max accessibility volume "+audioManager.getStreamMaxVolume(AudioManager.STREAM_ACCESSIBILITY));
//
//        }
//        else{
//            Log.i("check","SDK version problem");
//        }

//        int maxMediaVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
//        int desiredMediaVolume = (int) (maxMediaVolume * 0.6); // 70% of max volume
//        if(audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)>=desiredMediaVolume){
//            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, desiredMediaVolume, 0);
//        }
//
//
//        // For notification volume
//        int maxNotificationVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_NOTIFICATION);
//        int desiredNotificationVolume = (int) (maxNotificationVolume * 0.6); // 70% of max volume
//        audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, desiredNotificationVolume, 0);
//
//        // For alarm volume
//        int maxAlarmVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_ALARM);
//        int desiredAlarmVolume = (int) (maxAlarmVolume * 0.6); // 70% of max volume
//        audioManager.setStreamVolume(AudioManager.STREAM_ALARM, desiredAlarmVolume, 0);
        Log.i("check","volume changed");
    }


}