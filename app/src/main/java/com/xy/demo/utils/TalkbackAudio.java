package com.xy.demo.utils;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.annotation.SuppressLint;
import android.media.AudioManager;
import android.view.accessibility.AccessibilityEvent;

public class TalkbackAudio extends AccessibilityService {

    private AudioManager audioManager;
    private final AccessibilityServiceInfo info = new AccessibilityServiceInfo();
    private static final String TAGEVENTS = "TAGEVENTS";

    @Override
    public void onServiceConnected() {
        // Set the type of events that this service wants to listen to. Others
        // aren't passed to this service.
//        info.eventTypes = AccessibilityEvent.TYPE_VIEW_CLICKED |
//                AccessibilityEvent.TYPE_VIEW_FOCUSED;

        // If you only want this service to work with specific apps, set their
        // package names here. Otherwise, when the service is activated, it listens
        // to events from all apps.
//        info.packageNames = new String[]
//                {"com.example.android.myFirstApp", "com.example.android.mySecondApp"};
        info.eventTypes = AccessibilityEvent.TYPES_ALL_MASK;
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_ALL_MASK;

        info.notificationTimeout = 100;

        if ((info.flags & AccessibilityServiceInfo.FLAG_ENABLE_ACCESSIBILITY_VOLUME) == 0) {
            // If not, add flagEnableAccessibilityVolume
            info.flags |= AccessibilityServiceInfo.FLAG_ENABLE_ACCESSIBILITY_VOLUME;

            setServiceInfo(info);
        }
        // Set the type of feedback your service provides.
//        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_SPOKEN;

        // Default services are invoked only if no package-specific services are
        // present for the type of AccessibilityEvent generated. This service is
        // app-specific, so the flag isn't necessary. For a general-purpose service,
        // consider setting the DEFAULT flag.

        // info.flags = AccessibilityServiceInfo.DEFAULT;

//        info.notificationTimeout = 100;

//        this.setServiceInfo(info);

    }
    @SuppressLint("WrongConstant")
    @Override
    public void onAccessibilityEvent(AccessibilityEvent accessibilityEvent) {

//        AccessibilityNodeInfo interactedNodeInfo =
//                accessibilityEvent.getSource();
//        audioManager=(AudioManager) getSystemService(Context.AUDIO_SERVICE);
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_VIEW_SELECTED) {
//            Log.d(TAGEVENTS, "CONTENT_CHANGE_TYPE_CONTENT_DESCRIPTION");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
//            Log.d(TAGEVENTS, "TYPE_WINDOW_STATE_CHANGED");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_VIEW_CLICKED) {
//            Log.d(TAGEVENTS, "CONTENT_CHANGE_TYPE_SUBTREE");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_VIEW_LONG_CLICKED) {
//            Log.d(TAGEVENTS, "CONTENT_CHANGE_TYPE_TEXT");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.INVALID_POSITION) {
//            Log.d(TAGEVENTS, "INVALID_POSITION");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.CONTENT_CHANGE_TYPE_UNDEFINED) {
//            Log.d(TAGEVENTS, "CONTENT_CHANGE_TYPE_UNDEFINED");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_ANNOUNCEMENT) {
//            Log.d(TAGEVENTS, "TYPE_ANNOUNCEMENT");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT) {
//            Log.d(TAGEVENTS, "TYPE_ASSIST_READING_CONTEXT");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_GESTURE_DETECTION_END) {
//            Log.d(TAGEVENTS, "TYPE_GESTURE_DETECTION_END");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_VIEW_CLICKED) {
//            Log.d(TAGEVENTS, "TYPE_VIEW_CLICKED");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START) {
//            Log.d(TAGEVENTS, "TYPE_TOUCH_EXPLORATION_GESTURE_START");
//            Log.i("check","adjusting talkback audio");
//            int maxVolume=audioManager.getStreamMaxVolume(AudioManager.STREAM_ACCESSIBILITY);
//            int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_ACCESSIBILITY);
//            if (currentVolume > (int)(maxVolume*0.1)) {
//                audioManager.setStreamVolume(AudioManager.STREAM_ACCESSIBILITY,(int)(maxVolume*0.6),0);
//            }
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_GESTURE_DETECTION_START) {
//            Log.d(TAGEVENTS, "TYPE_GESTURE_DETECTION_START");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED) {
//            Log.d(TAGEVENTS, "TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
//            Log.d(TAGEVENTS, "TYPE_VIEW_ACCESSIBILITY_FOCUSED");
//        }
//        if (accessibilityEvent.getEventType() == AccessibilityEvent.TYPE_WINDOWS_CHANGED) {
//
//            Log.d(TAGEVENTS, "TYPE_WINDOWS_CHANGED");
//        }
//
//        Log.i("check","accessing talkback function");
//
//        if (interactedNodeInfo!=null&&interactedNodeInfo.getText()!=null&&interactedNodeInfo.getText().equals("Increase volume")) {
//            Log.i("check","adjusting talkback audio");
//        int maxVolume=audioManager.getStreamMaxVolume(AudioManager.STREAM_ACCESSIBILITY);
//        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_ACCESSIBILITY);
//            if (currentVolume > (int)(maxVolume*0.1)) {
//                audioManager.setStreamVolume(AudioManager.STREAM_ACCESSIBILITY,(int)(maxVolume*0.6),0);
//            }
//        }
    }

    @Override
    public void onInterrupt() {
    }
}
