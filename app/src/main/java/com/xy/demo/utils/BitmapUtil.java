package com.xy.demo.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.util.Log;
import android.util.LruCache;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 優化的位圖工具類，包含記憶體管理和位圖回收機制
 * 防止記憶體洩漏和提升性能
 */
public class BitmapUtil {
    private static final String TAG = "BitmapUtil";
    
    // 位圖緩存池，重用位圖以減少記憶體分配
    private static final LruCache<String, Bitmap> bitmapCache = new LruCache<String, Bitmap>(4 * 1024 * 1024) {
        @Override
        protected int sizeOf(String key, Bitmap bitmap) {
            return bitmap.getByteCount();
        }
        
        @Override
        protected void entryRemoved(boolean evicted, String key, Bitmap oldBitmap, Bitmap newBitmap) {
            // 當位圖從緩存中移除時，安全地回收
            if (oldBitmap != null && !oldBitmap.isRecycled()) {
                oldBitmap.recycle();
            }
        }
    };
    
    // 用於同步位圖操作的鎖
    private static final ReentrantLock bitmapLock = new ReentrantLock();
    
    /**
     * 優化的NV21轉Bitmap方法，包含完整的記憶體管理
     * @param data NV21格式的圖像數據
     * @param width 圖像寬度
     * @param height 圖像高度
     * @param rotation 旋轉角度
     * @param quality JPEG壓縮質量 (1-100)
     * @return 優化處理後的Bitmap，調用者負責回收
     */
    public static Bitmap nv21ToBitmap(byte[] data, int width, int height, int rotation, int quality) {
        if (data == null || data.length == 0) {
            Log.e(TAG, "Invalid input data for nv21ToBitmap");
            return null;
        }
        
        // 記錄記憶體使用情況
        logMemoryUsage("Before nv21ToBitmap");
        
        Bitmap originalBitmap = null;
        Bitmap rotatedBitmap = null;
        ByteArrayOutputStream stream = null;
        
        try {
            // 創建YuvImage並壓縮為JPEG
            YuvImage yuvImage = new YuvImage(data, ImageFormat.NV21, width, height, null);
            stream = new ByteArrayOutputStream();
            
            // 確保質量在有效範圍內
            int validQuality = Math.max(1, Math.min(100, quality));
            
            if (!yuvImage.compressToJpeg(new Rect(0, 0, width, height), validQuality, stream)) {
                Log.e(TAG, "Failed to compress YuvImage to JPEG");
                return null;
            }
            
            // 使用優化的BitmapFactory選項
            BitmapFactory.Options options = createOptimizedBitmapOptions();
            byte[] jpegData = stream.toByteArray();
            
            originalBitmap = BitmapFactory.decodeByteArray(jpegData, 0, jpegData.length, options);
            if (originalBitmap == null) {
                Log.e(TAG, "Failed to decode JPEG data to Bitmap");
                return null;
            }
            
            // 應用旋轉（如果需要）
            if (rotation != 0) {
                rotatedBitmap = rotateBitmapSafely(originalBitmap, rotation);
                
                // 回收原始位圖（如果成功創建了旋轉後的位圖）
                if (rotatedBitmap != null && rotatedBitmap != originalBitmap) {
                    recycleBitmapSafely(originalBitmap);
                    originalBitmap = null; // 防止重複回收
                }
            } else {
                rotatedBitmap = originalBitmap;
            }
            
            Log.d(TAG, String.format("Successfully created bitmap: %dx%d, bytes: %d", 
                rotatedBitmap.getWidth(), rotatedBitmap.getHeight(), rotatedBitmap.getByteCount()));
            
            return rotatedBitmap;
            
        } catch (Exception e) {
            Log.e(TAG, "Error in nv21ToBitmap", e);
            
            // 清理資源
            recycleBitmapSafely(originalBitmap);
            recycleBitmapSafely(rotatedBitmap);
            
            return null;
            
        } finally {
            // 確保流被關閉
            closeStreamSafely(stream);
            
            // 記錄處理後的記憶體使用情況
            logMemoryUsage("After nv21ToBitmap");
        }
    }
    
    /**
     * 優化的保存位圖方法，包含完整的資源管理
     * @param nv21 NV21格式的圖像數據
     * @param width 圖像寬度
     * @param height 圖像高度  
     * @param rotation 旋轉角度
     * @param quality JPEG壓縮質量
     * @param filepath 保存路徑
     * @return 是否成功保存
     */
    public static boolean saveToBitmap(byte[] nv21, int width, int height, int rotation, int quality, String filepath) {
        if (nv21 == null || filepath == null || filepath.trim().isEmpty()) {
            Log.e(TAG, "Invalid parameters for saveToBitmap");
            return false;
        }
        
        Log.d(TAG, String.format("Saving bitmap to: %s", filepath));
        
        Bitmap bitmap = null;
        FileOutputStream fos = null;
        
        try {
            // 創建位圖
            bitmap = nv21ToBitmap(nv21, width, height, rotation, quality);
            if (bitmap == null) {
                Log.e(TAG, "Failed to create bitmap from NV21 data");
                return false;
            }
            
            // 創建目標文件
            File file = new File(filepath);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    Log.e(TAG, "Failed to create parent directories");
                    return false;
                }
            }
            
            // 保存位圖到文件
            fos = new FileOutputStream(file);
            boolean success = bitmap.compress(Bitmap.CompressFormat.JPEG, 95, fos);
            
            if (success) {
                Log.d(TAG, String.format("Successfully saved bitmap to %s", filepath));
            } else {
                Log.e(TAG, "Failed to compress bitmap to file");
            }
            
            return success;
            
        } catch (Exception e) {
            Log.e(TAG, "Error saving bitmap to file", e);
            return false;
            
        } finally {
            // 清理資源
            recycleBitmapSafely(bitmap);
            closeStreamSafely(fos);
        }
    }
    
    /**
     * 創建優化的BitmapFactory選項
     */
    private static BitmapFactory.Options createOptimizedBitmapOptions() {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inPreferredConfig = Bitmap.Config.RGB_565; // 使用較少記憶體的格式
        options.inDither = false;
        options.inScaled = false;
        options.inPurgeable = true; // 允許系統回收像素記憶體
        options.inInputShareable = true;
        return options;
    }
    
    /**
     * 安全地旋轉位圖
     */
    private static Bitmap rotateBitmapSafely(Bitmap source, int rotation) {
        if (source == null || source.isRecycled()) {
            return null;
        }
        
        try {
            Matrix matrix = new Matrix();
            matrix.preRotate(rotation);
            
            Bitmap rotatedBitmap = Bitmap.createBitmap(
                source, 0, 0, source.getWidth(), source.getHeight(), matrix, true
            );
            
            Log.d(TAG, String.format("Rotated bitmap by %d degrees", rotation));
            return rotatedBitmap;
            
        } catch (OutOfMemoryError e) {
            Log.e(TAG, "OutOfMemoryError while rotating bitmap", e);
            // 觸發垃圾回收並重試
            System.gc();
            try {
                Thread.sleep(100);
                Matrix matrix = new Matrix();
                matrix.preRotate(rotation);
                return Bitmap.createBitmap(source, 0, 0, source.getWidth(), source.getHeight(), matrix, true);
            } catch (Exception retryE) {
                Log.e(TAG, "Failed to rotate bitmap after GC", retryE);
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error rotating bitmap", e);
            return null;
        }
    }
    
    /**
     * 安全地回收位圖
     */
    public static void recycleBitmapSafely(Bitmap bitmap) {
        if (bitmap != null && !bitmap.isRecycled()) {
            try {
                bitmap.recycle();
                Log.d(TAG, "Bitmap recycled successfully");
            } catch (Exception e) {
                Log.e(TAG, "Error recycling bitmap", e);
            }
        }
    }
    
    /**
     * 安全地關閉流
     */
    private static void closeStreamSafely(java.io.Closeable stream) {
        if (stream != null) {
            try {
                stream.close();
            } catch (IOException e) {
                Log.e(TAG, "Error closing stream", e);
            }
        }
    }
    
    /**
     * 記錄記憶體使用情況
     */
    private static void logMemoryUsage(String stage) {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            double usedPercentage = (double) usedMemory / maxMemory * 100;
            
            Log.d(TAG, String.format("Memory %s: %.1f%% (%d MB / %d MB)", 
                stage, usedPercentage, usedMemory / (1024 * 1024), maxMemory / (1024 * 1024)));
                
            if (usedPercentage > 75) {
                Log.w(TAG, "High memory usage detected: " + usedPercentage + "%");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error logging memory usage", e);
        }
    }
    
    /**
     * 清理位圖緩存
     */
    public static void clearBitmapCache() {
        bitmapLock.lock();
        try {
            bitmapCache.evictAll();
            Log.d(TAG, "Bitmap cache cleared");
        } finally {
            bitmapLock.unlock();
        }
    }
    
    /**
     * 獲取位圖緩存統計信息
     */
    public static String getCacheStats() {
        return String.format("Cache size: %d bytes, hit count: %d, miss count: %d", 
            bitmapCache.size(), bitmapCache.hitCount(), bitmapCache.missCount());
    }
}
