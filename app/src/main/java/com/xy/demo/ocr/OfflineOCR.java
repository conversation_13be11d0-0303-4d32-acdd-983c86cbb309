//package com.xy.demo.ocr;
//
//import android.graphics.Point;
//import android.graphics.Rect;
//import android.os.Handler;
//import android.os.Looper;
//import android.util.Log;
//
//import androidx.annotation.NonNull;
//
//import com.google.android.gms.tasks.OnFailureListener;
//import com.google.android.gms.tasks.OnSuccessListener;
//import com.google.android.gms.tasks.Task;
//import com.google.mlkit.vision.common.InputImage;
//import com.google.mlkit.vision.text.Text;
//import com.google.mlkit.vision.text.TextRecognition;
//import com.google.mlkit.vision.text.TextRecognizer;
//import com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions;
//
//public class OfflineOCR {
//    private TextRecognizer recognizer;
//    private final Handler mainThreadHandler = new Handler(Looper.getMainLooper());
//    private OCRCallback callback;
//    public OfflineOCR(OCRCallback callback){
//        this.recognizer =
//                TextRecognition.getClient(new ChineseTextRecognizerOptions.Builder().build());
//        this.callback = callback;
//    }
//
//    public void process(byte[] byteArray,int width, int height){
//        InputImage image = InputImage.fromByteArray(
//                byteArray,
//                width,
//                height,
//                270,
//                InputImage.IMAGE_FORMAT_NV21 // or IMAGE_FORMAT_YV12
//        );
//
//        Task<Text> result =
//                recognizer.process(image)
//                        .addOnSuccessListener(new OnSuccessListener<Text>() {
//                            @Override
//                            public void onSuccess(Text visionText) {
//                                Log.i("check OCR",visionText.getText());
//                                mainThreadHandler.post(new Runnable() {
//                                    @Override
//                                    public void run() {
//                                        callback.onProcessComplete("Offline:\n"+visionText.getText());
//                                        callback.onisProcessingChanged(false);
//                                    }
//                                });
//
//                                for (Text.TextBlock block : visionText.getTextBlocks()) {
//                                    Rect boundingBox = block.getBoundingBox();
//                                    Point[] cornerPoints = block.getCornerPoints();
//                                    String text = block.getText();
//                                    for (Text.Line line: block.getLines()) {
//                                        // ...
//                                        Log.i("check angle",String.valueOf(line.getAngle()));
//                                        Log.i("check conf",String.valueOf(line.getConfidence()));
//                                        for (Text.Element element: line.getElements()) {
//                                            // ...
//                                            for (Text.Symbol symbol: element.getSymbols()) {
//                                                // ...
//
//                                            }
//                                        }
//                                    }
//                                }
//                            }
//                        })
//                        .addOnFailureListener(
//                                new OnFailureListener() {
//                                    @Override
//                                    public void onFailure(@NonNull Exception e) {
//                                        // Task failed with an exception
//                                        // ...
//                                    }
//                                });
//    }
//
//}

package com.xy.demo.ocr;

import android.annotation.SuppressLint;
import android.graphics.Point;
import android.graphics.Rect;
import android.media.Image;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.camera.core.ImageProxy; // 導入 ImageProxy

import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.text.Text;
import com.google.mlkit.vision.text.TextRecognition;
import com.google.mlkit.vision.text.TextRecognizer;
import com.google.mlkit.vision.text.latin.TextRecognizerOptions;

import java.lang.ref.WeakReference;

public class OfflineOCR {
    // 使用WeakReference防止OCRCallback導致的記憶體洩漏
    private final WeakReference<OCRCallback> callbackRef;
    private final TextRecognizer recognizer;

    public OfflineOCR(OCRCallback callback) {
        this.callbackRef = new WeakReference<>(callback);
        recognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS);
    }
    
    /**
     * 安全地獲取OCRCallback引用
     * @return OCRCallback實例，如果已被回收則返回null
     */
    private OCRCallback getCallback() {
        return callbackRef.get();
    }

    // 更新：直接接收 ImageProxy 物件，優化內存使用
    public void process(ImageProxy imageProxy) {
        // 關鍵改動：使用 fromMediaImage 方法，這是 CameraX -> ML Kit 的標準做法
        // 它能正確處理 YUV_420_888 格式和旋轉角度
        @SuppressLint("UnsafeOptInUsageError")
        Image mediaImage = imageProxy.getImage();
        if (mediaImage != null) {
            InputImage image = InputImage.fromMediaImage(mediaImage, imageProxy.getImageInfo().getRotationDegrees());

            // 使用異步處理，避免阻塞主線程
            Task<Text> result = recognizer.process(image)
                    .addOnSuccessListener(visionText -> {
                        Log.i("OfflineOCR", "Text recognition success.");
                        
                        // 安全地獲取callback引用
                        OCRCallback callback = getCallback();
                        if (callback != null) {
                            // 優化：基於信心分數過濾和處理文本結果
                            Log.i("OfflineOCR","raw result"+visionText.getText());
                            String processedText = processTextWithConfidenceFiltering(visionText);
                            callback.onProcessComplete(processedText);
                            callback.onisProcessingChanged(false);
                        } else {
                            Log.w("OfflineOCR", "OCRCallback has been garbage collected, skipping result delivery");
                        }
                    })
                    .addOnFailureListener(e -> {
                        Log.e("OfflineOCR", "Text recognition failed.", e);
                        
                        // 安全地獲取callback引用
                        OCRCallback callback = getCallback();
                        if (callback != null) {
                            callback.onProcessComplete(""); // 回傳空字串表示失敗
                            callback.onisProcessingChanged(false);
                        } else {
                            Log.w("OfflineOCR", "OCRCallback has been garbage collected, skipping error delivery");
                        }
                    })
                    .addOnCompleteListener(task -> {
                        // 關鍵：無論成功或失敗，都必須關閉 ImageProxy
                        imageProxy.close();
                    });
        } else {
            // 如果無法獲取 mediaImage，也要關閉
            imageProxy.close();
            
            // 安全地獲取callback引用
            OCRCallback callback = getCallback();
            if (callback != null) {
                callback.onisProcessingChanged(false);
            } else {
                Log.w("OfflineOCR", "OCRCallback has been garbage collected, skipping processing state update");
            }
        }
    }

    /**
     * 基於信心分數過濾和處理OCR結果
     * 提供更智能的文本提取和品質控制
     * @param visionText ML Kit識別的Text對象
     * @return 處理後的高品質文本
     */
    private String processTextWithConfidenceFiltering(Text visionText) {
        if (visionText == null) {
            return "";
        }
        
        StringBuilder filteredText = new StringBuilder();
        float totalConfidence = 0f;
        int blockCount = 0;
        
        // 分析所有文本塊並應用信心過濾
        for (Text.TextBlock block : visionText.getTextBlocks()) {
            String blockText = processTextBlock(block);
            if (!blockText.trim().isEmpty()) {
                filteredText.append(blockText).append(" ");
                blockCount++;
            }
        }
        
        // 如果沒有高信心的文本塊，回退到基本處理
        if (filteredText.length() == 0) {
            Log.w("OfflineOCR", "No high-confidence text found, using fallback processing");
            return preprocessText(visionText.getText());
        }
        
        // 應用高級文本處理
        String processedText = preprocessText(filteredText.toString());
        
        // 智能文本分塊以優化TTS性能
        return applyIntelligentChunking(processedText);
    }
    
    /**
     * 處理單個文本塊，基於信心分數和文本品質
     * @param block Text.TextBlock對象
     * @return 過濾後的文本塊內容
     */
    private String processTextBlock(Text.TextBlock block) {
        if (block == null) return "";
        
        StringBuilder blockBuilder = new StringBuilder();
        float blockConfidenceSum = 0f;
        int lineCount = 0;
        
        for (Text.Line line : block.getLines()) {
            String lineText = processTextLine(line);
            if (!lineText.trim().isEmpty()) {
                blockBuilder.append(lineText).append(" ");
                lineCount++;
            }
        }
        
        // 如果文本塊太短或沒有內容，可能是噪音
        String blockText = blockBuilder.toString().trim();
        if (blockText.length() < 2) {
            return "";
        }
        
        return blockText;
    }
    
    /**
     * 處理單行文本，基於元素信心分數
     * @param line Text.Line對象
     * @return 過濾後的行文本
     */
    private String processTextLine(Text.Line line) {
        if (line == null) return "";
        
        // 使用行級別的信心分數（如果可用）
        Float lineConfidence = line.getConfidence();
        final float MIN_LINE_CONFIDENCE = 0.5f;
        
        // 如果整行信心分數太低，跳過
        if (lineConfidence != null && lineConfidence < MIN_LINE_CONFIDENCE) {
            Log.d("OfflineOCR", String.format("Skipping low-confidence line: %.2f", lineConfidence));
            return "";
        }
        
        StringBuilder lineBuilder = new StringBuilder();
        
        for (Text.Element element : line.getElements()) {
            String elementText = processTextElement(element);
            if (!elementText.trim().isEmpty()) {
                lineBuilder.append(elementText).append(" ");
            }
        }
        
        return lineBuilder.toString().trim();
    }
    
    /**
     * 處理文本元素，應用最細粒度的信心過濾
     * @param element Text.Element對象
     * @return 過濾後的元素文本
     */
    private String processTextElement(Text.Element element) {
        if (element == null) return "";
        
        String elementText = element.getText();
        Float elementConfidence = element.getConfidence();
        
        // 設定最小信心閾值
        final float MIN_ELEMENT_CONFIDENCE = 0.5f;
        
        // 信心分數過濾
        if (elementConfidence != null && elementConfidence < MIN_ELEMENT_CONFIDENCE) {
            Log.d("OfflineOCR", String.format("Filtering low-confidence element: '%s' (%.2f)", 
                elementText, elementConfidence));
            return "";
        }
        
        // 額外的文本品質檢查
        if (isValidTextElement(elementText)) {
            return elementText;
        }
        
        return "";
    }
    
    /**
     * 驗證文本元素是否為有效內容
     * @param text 文本內容
     * @return 是否為有效文本
     */
    private boolean isValidTextElement(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        
        // 移除過短的文本（可能是噪音）
        if (text.trim().length() < 1) {
            return false;
        }
        
        // 檢查是否主要由特殊字符組成（可能是OCR錯誤）
        String cleaned = text.replaceAll("[\\p{P}\\p{S}\\s]", "");
        if (cleaned.length() == 0) {
            return false;
        }
        
        // 檢查重複字符模式（OCR常見錯誤）
        if (text.matches("(.)\\1{4,}")) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 智能文本分塊，針對TTS優化
     * 將長文本分割為適合語音合成的較小塊
     * @param text 預處理後的文本
     * @return 智能分塊後的文本
     */
    private String applyIntelligentChunking(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        final int OPTIMAL_CHUNK_SIZE = 150; // TTS最佳處理長度
        final int MAX_CHUNK_SIZE = 200;     // 最大允許長度
        
        if (text.length() <= OPTIMAL_CHUNK_SIZE) {
            return text.trim();
        }
        
        StringBuilder chunkedText = new StringBuilder();
        String[] sentences = splitIntoSentences(text);
        StringBuilder currentChunk = new StringBuilder();
        
        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.isEmpty()) continue;
            
            // 檢查添加這個句子是否會超過最佳長度
            if (currentChunk.length() + sentence.length() + 1 > OPTIMAL_CHUNK_SIZE && 
                currentChunk.length() > 0) {
                
                // 當前塊已達到最佳長度，開始新塊
                chunkedText.append(currentChunk.toString().trim()).append("\n\n");
                currentChunk = new StringBuilder();
            }
            
            currentChunk.append(sentence).append(" ");
            
            // 如果單個句子就超過最大長度，強制分割
            if (currentChunk.length() > MAX_CHUNK_SIZE) {
                chunkedText.append(currentChunk.toString().trim()).append("\n\n");
                currentChunk = new StringBuilder();
            }
        }
        
        // 添加最後一塊
        if (currentChunk.length() > 0) {
            chunkedText.append(currentChunk.toString().trim());
        }
        
        return chunkedText.toString().trim();
    }
    
    /**
     * 將文本按句子分割，支持中英文混合
     * @param text 輸入文本
     * @return 句子數組
     */
    private String[] splitIntoSentences(String text) {
        // 支持中英文句子分割
        return text.split("(?<=[。！？.!?])\\s*");
    }

    /**
     * 高級OCR文本預處理，專為TTS語音合成優化
     * 包含文本正規化、特殊字符處理、語言特定格式化
     */
    private String preprocessText(String rawText) {
        if (rawText == null || rawText.trim().isEmpty()) {
            return "";
        }
        
        // 基本清理和正規化
        String processedText = rawText.trim()
                .replaceAll("\\s+", " ") // 多個空白字符替換為單一空格
                .replaceAll("\\n+", " ") // 將換行符替換為空格，便於TTS連續播放
                .replaceAll("\\r", ""); // 移除回車符
        
        // 數字和特殊字符正規化（適合TTS朗讀）
        processedText = normalizeNumbersAndSymbols(processedText);
        
        // 標點符號優化（添加適當的停頓）
        processedText = optimizePunctuation(processedText);
        
        // 移除OCR常見錯誤字符
        processedText = removeOcrArtifacts(processedText);
        
        // 語言特定格式化
        processedText = applyLanguageSpecificFormatting(processedText);
        
        // 文本長度優化（分塊處理）
        processedText = optimizeTextLength(processedText);
        
        return processedText.trim();
    }
    
    /**
     * 正規化數字和符號以便TTS更好朗讀
     */
    private String normalizeNumbersAndSymbols(String text) {
        return text
                // 貨幣符號標準化
                .replaceAll("[$￥¥€£]", "$0 ") // 在貨幣符號後添加空格
                .replaceAll("(%|℃|°C|°F)", " $1") // 在百分號和溫度符號前添加空格
                // 時間格式優化
                .replaceAll("(\\d{1,2}):(\\d{2})", "$1點$2分") // 12:30 -> 12點30分
                // 日期格式優化  
                .replaceAll("(\\d{4})[-/](\\d{1,2})[-/](\\d{1,2})", "$1年$2月$3日")
                // 電話號碼格式化（添加適當停頓）
                .replaceAll("(\\d{3,4})-?(\\d{3,4})-?(\\d{4})", "$1-$2-$3");
    }
    
    /**
     * 優化標點符號以提供更好的TTS語音停頓
     */
    private String optimizePunctuation(String text) {
        return text
                // 確保句號後有適當停頓
                .replaceAll("([。.])([A-Za-z\\u4e00-\\u9fff])", "$1 $2")
                // 確保逗號後有輕微停頓
                .replaceAll("([，,])([A-Za-z\\u4e00-\\u9fff])", "$1 $2")
                // 問號和感嘆號後添加停頓
                .replaceAll("([？?！!])([A-Za-z\\u4e00-\\u9fff])", "$1 $2")
                // 冒號和分號優化
                .replaceAll("([：:；;])([A-Za-z\\u4e00-\\u9fff])", "$1 $2");
    }
    
    /**
     * 移除OCR常見的錯誤識別字符和噪音
     */
    private String removeOcrArtifacts(String text) {
        return text
                // 移除常見OCR錯誤字符
                .replaceAll("[\\|\\\\/_]+", " ") // 移除豎線、反斜線等
                .replaceAll("[~`@#$%^&*()+=\\[\\]{}\\\\|;:'\",.<>?]", " ") // 移除可能的噪音字符
                // 移除重複字符（可能是OCR錯誤）
                .replaceAll("(.)\\1{3,}", "$1$1") // 超過3個重複字符縮減為2個
                // 移除孤立的單個字符（可能是噪音）
                .replaceAll("\\b[a-zA-Z]\\b", " ")
                // 清理多餘空格
                .replaceAll("\\s+", " ");
    }
    
    /**
     * 應用語言特定格式化規則
     */
    private String applyLanguageSpecificFormatting(String text) {
        // 檢測主要語言（簡單啟發式方法）
        boolean containsChinese = text.matches(".*[\\u4e00-\\u9fff].*");
        boolean containsEnglish = text.matches(".*[a-zA-Z].*");
        
        if (containsChinese) {
            // 中文特定優化
            text = text
                    // 確保中英文之間有適當間隔
                    .replaceAll("([\\u4e00-\\u9fff])([a-zA-Z])", "$1 $2")
                    .replaceAll("([a-zA-Z])([\\u4e00-\\u9fff])", "$1 $2")
                    // 數位和中文間的間隔
                    .replaceAll("([\\u4e00-\\u9fff])(\\d)", "$1 $2")
                    .replaceAll("(\\d)([\\u4e00-\\u9fff])", "$1 $2");
        }
        
        if (containsEnglish && !containsChinese) {
            // 純英文優化
            text = text
                    // 確保縮寫後有停頓
                    .replaceAll("(\\b[A-Z]{2,}\\b)", " $1 ")
                    // 優化常見縮寫
                    .replaceAll("\\bDr\\.", "Doctor")
                    .replaceAll("\\bMr\\.", "Mister")
                    .replaceAll("\\bMrs\\.", "Misses");
        }
        
        return text.replaceAll("\\s+", " ").trim();
    }
    
    /**
     * 優化文本長度，避免TTS超時
     * 將長文本分割成適合語音合成的塊
     */
    private String optimizeTextLength(String text) {
        final int MAX_CHUNK_LENGTH = 200; // TTS最佳處理長度
        
        if (text.length() <= MAX_CHUNK_LENGTH) {
            return text;
        }
        
        // 在句子邊界分割文本
        String[] sentences = text.split("(?<=[。.!?！？])\\s*");
        StringBuilder optimizedText = new StringBuilder();
        StringBuilder currentChunk = new StringBuilder();
        
        for (String sentence : sentences) {
            if (currentChunk.length() + sentence.length() > MAX_CHUNK_LENGTH && currentChunk.length() > 0) {
                // 當前塊達到最大長度，添加分隔標記
                optimizedText.append(currentChunk.toString().trim()).append("\n");
                currentChunk = new StringBuilder();
            }
            currentChunk.append(sentence).append(" ");
        }
        
        // 添加最後一塊
        if (currentChunk.length() > 0) {
            optimizedText.append(currentChunk.toString().trim());
        }
        
        return optimizedText.toString();
    }
}
