package com.xy.demo.ocr;

import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.OptIn;
import androidx.camera.core.ExperimentalGetImage;
import androidx.camera.core.ImageProxy;

import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.vision.v1.Vision;
import com.google.api.services.vision.v1.VisionRequest;
import com.google.api.services.vision.v1.VisionRequestInitializer;
import com.google.api.services.vision.v1.model.*;
import com.xy.demo.utils.BitmapUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 企業級 Google Cloud Vision OCR 解決方案
 *
 * 核心特性：
 * 1. 完全兼容現有API，無需修改調用代碼
 * 2. 原生支持 ImageProxy (CameraX) + byte[] 雙重輸入
 * 3. 簡化的文本後處理，專注於UI顯示優化
 * 4. 智能重試機制和錯誤處理
 * 5. WeakReference 記憶體安全管理
 * 6. 企業級性能監控和統計
 *
 * 文本後處理策略：
 * - 使用 Google Vision API 默認的 getFullTextAnnotation().getText() 結果
 * - 執行最小化的文本標準化，僅用於UI顯示優化
 * - 保持原始文本結構和意義，不進行語義轉換
 */
public class GoogleOCR {
    private static final String TAG = "GoogleOCR";
    
    // 配置常量
    private static final int MAX_RETRY_ATTEMPTS = 2;
    private static final int NETWORK_TIMEOUT_MS = 10000;
    private static final int IMAGE_QUALITY = 95;
    
    // 性能監控
    private final AtomicInteger totalRequests = new AtomicInteger(0);
    private final AtomicInteger successfulRequests = new AtomicInteger(0);
    private final AtomicInteger failedRequests = new AtomicInteger(0);
    
    // 線程管理 - 與現有API兼容
    private final ExecutorService executorService;
    private final Handler mainThreadHandler;
    
    // Vision API 客戶端配置緩存
    private Vision visionClient;
    private final Object visionClientLock = new Object();

    public GoogleOCR() {
        this.mainThreadHandler = new Handler(Looper.getMainLooper());
        
        // 優化的線程池配置
        this.executorService = Executors.newFixedThreadPool(2, new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "GoogleOCR-Thread-" + threadNumber.getAndIncrement());
                thread.setDaemon(true); // 防止阻止應用退出
                return thread;
            }
        });
        
        Log.i(TAG, "Enhanced GoogleOCR initialized successfully");
    }
    
    /**
     * 現有API兼容方法 - 完全保持原始簽名
     * @param apiKey Google Cloud Vision API密鑰
     * @param frameByte 圖像byte[]數據
     * @param framewidth 圖像寬度
     * @param frameheight 圖像高度  
     * @param callback 結果回調
     */
    public void performOCR(String apiKey, byte[] frameByte, int framewidth, int frameheight, OCRCallback callback) {
        totalRequests.incrementAndGet();
        callback.onisProcessingChanged(true);
        
        // 使用增強版任務處理，但保持完全兼容
        EnhancedOCRTask task = new EnhancedOCRTask(apiKey, frameByte, framewidth, frameheight, 
            new WeakReference<>(callback));
        executorService.submit(task);
    }
    
    /**
     * 新增ImageProxy支持方法（可選使用）
     * @param apiKey Google Cloud Vision API密鑰
     * @param imageProxy CameraX的ImageProxy對象
     * @param callback 結果回調
     */
    @OptIn(markerClass = ExperimentalGetImage.class)
    public void processImageProxy(String apiKey, @NonNull ImageProxy imageProxy, @NonNull OCRCallback callback) {
        totalRequests.incrementAndGet();
        callback.onisProcessingChanged(true);
        
        try {
            // 立即提取ImageProxy數據，避免在線程池中訪問已關閉的ImageProxy
            byte[] imageData = extractImageDataFromProxy(imageProxy);
            if (imageData != null) {
                // 使用提取的數據創建任務
                EnhancedOCRTask task = new EnhancedOCRTask(apiKey, imageData, 
                    imageProxy.getWidth(), imageProxy.getHeight(), new WeakReference<>(callback));
                executorService.submit(task);
            } else {
                // 提取失敗，直接回調錯誤
                failedRequests.incrementAndGet();
                Log.e(TAG, "Failed to extract image data from ImageProxy");
                callback.onProcessComplete("");
                callback.onisProcessingChanged(false);
            }
        } catch (Exception e) {
            failedRequests.incrementAndGet();
            Log.e(TAG, "Error extracting ImageProxy data", e);
            callback.onProcessComplete("");
            callback.onisProcessingChanged(false);
        } finally {
            // 確保ImageProxy在數據提取後立即關閉
            imageProxy.close();
        }
    }
    
    /**
     * 立即從ImageProxy提取圖像數據（主線程安全）
     * 支持多種圖像格式：YUV_420_888, JPEG等
     * @param imageProxy CameraX的ImageProxy對象
     * @return 提取的圖像數據，失敗時返回null
     */
    @OptIn(markerClass = ExperimentalGetImage.class)
    private byte[] extractImageDataFromProxy(@NonNull ImageProxy imageProxy) {
        try {
            android.media.Image image = imageProxy.getImage();
            if (image == null) {
                Log.e(TAG, "ImageProxy contains null image");
                return null;
            }
            
            int format = image.getFormat();
            Log.d(TAG, String.format("Processing ImageProxy: format=%d, planes=%d, size=%dx%d", 
                format, image.getPlanes().length, imageProxy.getWidth(), imageProxy.getHeight()));
            
            // 根據圖像格式進行不同的處理
            switch (format) {
                case android.graphics.ImageFormat.JPEG:
                    return extractJpegData(image);
                    
                case android.graphics.ImageFormat.YUV_420_888:
                    return extractYuvData(image);
                    
                default:
                    // 對於未知格式，嘗試提取第一個平面的數據
                    Log.w(TAG, "Unknown image format: " + format + ", attempting to extract first plane");
                    return extractFirstPlaneData(image);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error extracting image data from ImageProxy", e);
            return null;
        }
    }
    
    /**
     * 提取JPEG格式的圖像數據
     */
    private byte[] extractJpegData(android.media.Image image) {
        ByteBuffer buffer = image.getPlanes()[0].getBuffer();
        byte[] data = new byte[buffer.remaining()];
        buffer.get(data);
        Log.d(TAG, String.format("Extracted JPEG data: %d bytes", data.length));
        return data;
    }
    
    /**
     * 提取YUV_420_888格式的圖像數據，轉換為NV21
     */
    private byte[] extractYuvData(android.media.Image image) {
        android.media.Image.Plane[] planes = image.getPlanes();
        if (planes.length < 3) {
            Log.e(TAG, "YUV_420_888 format should have 3 planes, but got: " + planes.length);
            return null;
        }
        
        ByteBuffer yBuffer = planes[0].getBuffer();
        ByteBuffer uBuffer = planes[1].getBuffer();
        ByteBuffer vBuffer = planes[2].getBuffer();
        
        int ySize = yBuffer.remaining();
        int uSize = uBuffer.remaining();
        int vSize = vBuffer.remaining();
        
        byte[] nv21 = new byte[ySize + uSize + vSize];
        
        yBuffer.get(nv21, 0, ySize);
        vBuffer.get(nv21, ySize, vSize);
        uBuffer.get(nv21, ySize + vSize, uSize);
        
        Log.d(TAG, String.format("Extracted YUV data: %d bytes (Y:%d, U:%d, V:%d)", 
            nv21.length, ySize, uSize, vSize));
        
        return nv21;
    }
    
    /**
     * 提取第一個平面的數據（未知格式的回退方案）
     */
    private byte[] extractFirstPlaneData(android.media.Image image) {
        android.media.Image.Plane[] planes = image.getPlanes();
        if (planes.length == 0) {
            Log.e(TAG, "No planes available in image");
            return null;
        }
        
        ByteBuffer buffer = planes[0].getBuffer();
        byte[] data = new byte[buffer.remaining()];
        buffer.get(data);
        
        Log.d(TAG, String.format("Extracted first plane data: %d bytes from %d planes", 
            data.length, planes.length));
        
        return data;
    }
    
    /**
     * 增強版OCR處理任務
     * 整合所有先進功能但保持API兼容性
     */
    private class EnhancedOCRTask implements Runnable {
        private final String apiKey;
        private final byte[] frameData;
        private final int frameWidth, frameHeight;
        private final WeakReference<OCRCallback> callbackRef;
        private final long startTime;
        
        // byte[]構造函數（統一的數據處理方式）
        public EnhancedOCRTask(String apiKey, byte[] frameData, int width, int height, 
                               WeakReference<OCRCallback> callbackRef) {
            this.apiKey = apiKey;
            this.frameData = frameData;
            this.frameWidth = width;
            this.frameHeight = height;
            this.callbackRef = callbackRef;
            this.startTime = System.currentTimeMillis();
        }
        
        @Override
        public void run() {
            String result = "";
            boolean success = false;
            
            try {
                // 1. 圖像預處理
                byte[] imageData = preprocessImage();
                if (imageData == null) {
                    Log.e(TAG, "Failed to preprocess image");
                    notifyResult("", false);
                    return;
                }
                
                // 2. 執行OCR（帶重試機制）
                result = performOCRWithRetry(imageData);
                
                // 3. UI顯示文本標準化
                result = normalizeTextForDisplay(result);
                
                success = !result.isEmpty();
                
                if (success) {
                    successfulRequests.incrementAndGet();
                    long processingTime = System.currentTimeMillis() - startTime;
                    Log.i(TAG, String.format("OCR completed successfully in %dms, result length: %d", 
                        processingTime, result.length()));
                } else {
                    failedRequests.incrementAndGet();
                    Log.w(TAG, "OCR completed but no text detected");
                }
                
            } catch (Exception e) {
                failedRequests.incrementAndGet();
                Log.e(TAG, "Enhanced OCR processing failed", e);
                result = "";
                
            } finally {
                // ImageProxy已在主線程中關閉，這裡只需要通知結果
                notifyResult(result, false);
            }
        }
        
        /**
         * 圖像預處理 - 智能處理不同格式的圖像數據
         */
        private byte[] preprocessImage() {
            try {
                if (frameData == null) {
                    Log.e(TAG, "Frame data is null");
                    return null;
                }
                
                // 檢查是否已經是JPEG格式
                if (isJpegData(frameData)) {
                    Log.d(TAG, String.format("Input data is already JPEG format: %d bytes", frameData.length));
                    return frameData; // 直接返回JPEG數據
                }
                
                // 嘗試使用NV21格式創建Bitmap
                Bitmap bitmap = BitmapUtil.nv21ToBitmap(frameData, frameWidth, frameHeight, 0, IMAGE_QUALITY);
                
                if (bitmap == null) {
                    Log.w(TAG, "Failed to create bitmap using NV21 format, trying direct decoding");
                    // 嘗試直接解碼（可能是其他格式）
                    try {
                        bitmap = android.graphics.BitmapFactory.decodeByteArray(frameData, 0, frameData.length);
                        if (bitmap != null) {
                            Log.d(TAG, "Successfully decoded image using BitmapFactory");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to decode image data", e);
                    }
                }
                
                if (bitmap == null) {
                    Log.e(TAG, "Failed to create bitmap from frame data using all methods");
                    return null;
                }
                
                // 轉換為高質量JPEG
                ByteArrayOutputStream stream = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, stream);
                byte[] result = stream.toByteArray();
                
                // 清理資源
                if (!bitmap.isRecycled()) {
                    bitmap.recycle();
                }
                stream.close();
                
                Log.d(TAG, String.format("Image preprocessing completed: %dx%d -> JPEG %d bytes", 
                    frameWidth, frameHeight, result.length));
                
                return result;
                
            } catch (Exception e) {
                Log.e(TAG, "Error in image preprocessing", e);
                return null;
            }
        }
        
        /**
         * 檢查數據是否為JPEG格式
         */
        private boolean isJpegData(byte[] data) {
            if (data == null || data.length < 2) {
                return false;
            }
            
            // JPEG文件以 0xFF 0xD8 開頭
            return (data[0] & 0xFF) == 0xFF && (data[1] & 0xFF) == 0xD8;
        }
        
        /**
         * 帶重試機制的OCR執行
         */
        private String performOCRWithRetry(byte[] imageData) {
            String result = "";
            Exception lastException = null;
            
            for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
                try {
                    result = performSingleOCR(imageData);
                    if (!result.isEmpty()) {
                        return result; // 成功，返回結果
                    }
                } catch (IOException e) {
                    lastException = e;
                    Log.w(TAG, String.format("OCR attempt %d/%d failed: %s", attempt, MAX_RETRY_ATTEMPTS, e.getMessage()));
                    
                    if (attempt < MAX_RETRY_ATTEMPTS) {
                        try {
                            Thread.sleep(1000 * attempt); // 指數退避
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                } catch (Exception e) {
                    lastException = e;
                    Log.e(TAG, "Unexpected error during OCR", e);
                    break; // 非網絡錯誤，不重試
                }
            }
            
            if (lastException != null) {
                Log.e(TAG, "All OCR attempts failed", lastException);
            }
            
            return result;
        }
        
        /**
         * 執行單次OCR請求 - 使用DOCUMENT_TEXT_DETECTION
         */
        private String performSingleOCR(byte[] imageData) throws IOException {
            // 獲取或創建Vision客戶端
            Vision vision = getOrCreateVisionClient();
            
            Image image = new Image().encodeContent(imageData);
            
            // 使用DOCUMENT_TEXT_DETECTION獲得更好的文檔識別效果
            Feature feature = new Feature()
                    .setType("DOCUMENT_TEXT_DETECTION")
                    .setMaxResults(1);
            
            // 設置語言提示和圖像上下文（與TTS語言檢測協調）
            ImageContext imageContext = createOptimizedImageContext();
            
            AnnotateImageRequest request = new AnnotateImageRequest()
                    .setImage(image)
                    .setFeatures(Arrays.asList(feature))
                    .setImageContext(imageContext);
            
            BatchAnnotateImagesRequest batchRequest = new BatchAnnotateImagesRequest()
                    .setRequests(Arrays.asList(request));
            
            // 執行API調用
            Vision.Images.Annotate annotate = vision.images().annotate(batchRequest);
            annotate.setDisableGZipContent(true);
            BatchAnnotateImagesResponse response = annotate.execute();
            
            // 解析響應
            return parseOCRResponse(response);
        }
        
        /**
         * 獲取或創建Vision客戶端（線程安全，緩存重用）
         */
        private Vision getOrCreateVisionClient() {
            synchronized (visionClientLock) {
                if (visionClient == null) {
                    try {
                        HttpTransport httpTransport = new NetHttpTransport();
                        JsonFactory jsonFactory = GsonFactory.getDefaultInstance();
                        
                        Vision.Builder builder = new Vision.Builder(httpTransport, jsonFactory, null)
                                .setApplicationName("InnoText-Enhanced-OCR");
                        
                        builder.setVisionRequestInitializer(new VisionRequestInitializer(apiKey) {
                            @Override
                            protected void initializeVisionRequest(VisionRequest<?> visionRequest) throws IOException {
                                super.initializeVisionRequest(visionRequest);
                                // 保持現有的項目配置
                                visionRequest.getRequestHeaders().set("X-Android-Cert", "68f82aa766767ce1ac85a08b159c2fef19501f28");
                                visionRequest.getRequestHeaders().set("X-Android-Package", "hk.com.redso.read4u");
                            }
                        });
                        
                        visionClient = builder.build();
                        Log.i(TAG, "Vision client initialized successfully");
                        
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to initialize Vision client", e);
                        throw new RuntimeException("Vision client initialization failed", e);
                    }
                }
                return visionClient;
            }
        }
        
        /**
         * 創建優化的圖像上下文（與TTS語言檢測協調）
         */
        private ImageContext createOptimizedImageContext() {
            ImageContext imageContext = new ImageContext();
            
            // 設置語言提示（與AzureTextSynthesis的語言檢測保持一致）
            List<String> languageHints = Arrays.asList(
                "zh-Hant",  // 繁體中文（優先級最高）
                "zh-Hans",  // 簡體中文
                "en"        // 英文
            );
            imageContext.setLanguageHints(languageHints);
            
            return imageContext;
        }
        
        /**
         * 解析OCR響應 - 使用Google Vision API默認結果
         */
        private String parseOCRResponse(BatchAnnotateImagesResponse response) {
            if (response.getResponses() == null || response.getResponses().isEmpty()) {
                return "";
            }
            
            AnnotateImageResponse imageResponse = response.getResponses().get(0);
            
            // 檢查API錯誤
            if (imageResponse.getError() != null) {
                Log.e(TAG, "Google Vision API error: " + imageResponse.getError().getMessage());
                return "";
            }
            
            // 直接使用Google Cloud Vision API的默認文本結果
            if (imageResponse.getFullTextAnnotation() != null) {
                String text = imageResponse.getFullTextAnnotation().getText();
                Log.i(TAG,"default result"+"\n"+text);
                return text != null ? text : "";
            }
            
            return "";
        }

        
        /**
         * 增強的文本後處理 - UI顯示優化與OCR錯誤修正
         *
         * 執行以下處理步驟：
         * 1. 基本修剪：移除整個文本的前後空白
         * 2. 行尾標準化：將CRLF轉換為LF，保留換行符
         * 3. OCR錯誤修正：修正常見的OCR識別錯誤
         * 4. 行內空白清理：將每行內的多個連續空格替換為單個空格，修剪每行的前後空格
         * 5. 段落分隔：將3個或更多連續換行符壓縮為2個換行符（表示一個空行用於段落分隔）
         *
         * @param rawText 原始OCR文本
         * @return 標準化並修正後的文本，保持原始結構和意義
         */
        private String normalizeTextForDisplay(String rawText) {
            if (rawText == null || rawText.trim().isEmpty()) {
                return "";
            }

            // 1. 基本修剪：移除整個文本的前後空白
            String processed = rawText.trim();

            // 2. 行尾標準化：將所有CRLF序列轉換為LF
            processed = processed.replaceAll("\\r\\n", "\n");

            // 3. OCR錯誤修正：選擇性修正策略
            processed = correctOCRErrorsSelective(processed);

            // 4. 行內空白清理：處理每行內的空白字符
            processed = cleanupWhitespaceWithinLines(processed);

            // 5. 段落分隔：壓縮多個連續換行符
            processed = normalizeLineBreaks(processed);

            Log.d(TAG, "UI display text normalization and OCR error correction completed");
            return processed;
        }

        /**
         * 清理每行內的空白字符，同時保留行結構
         * @param text 輸入文本
         * @return 清理後的文本
         */
        private String cleanupWhitespaceWithinLines(String text) {
            if (text == null) {
                return "";
            }

            // 按行分割文本
            String[] lines = text.split("\n", -1); // -1 保留空行
            StringBuilder result = new StringBuilder();

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i];

                // 將每行內的多個連續空格替換為單個空格，並修剪前後空格
                String cleanedLine = line.replaceAll("[ \\t]+", " ").trim();

                result.append(cleanedLine);

                // 如果不是最後一行，添加換行符
                if (i < lines.length - 1) {
                    result.append("\n");
                }
            }

            return result.toString();
        }

        /**
         * 標準化換行符，壓縮多個連續換行符為段落分隔
         * @param text 輸入文本
         * @return 標準化後的文本
         */
        private String normalizeLineBreaks(String text) {
            if (text == null) {
                return "";
            }

            // 將3個或更多連續換行符壓縮為2個換行符（一個空行用於段落分隔）
            return text.replaceAll("\n{3,}", "\n\n");
        }

        /**
         * OCR錯誤修正 - 修正常見的OCR識別錯誤
         *
         * 處理以下類型的錯誤：
         * 0. 特殊符號和噪音過濾：移除無法朗讀的符號和無意義內容
         * 1. 字符替換錯誤：修正常見的字符混淆（如0/O, 1/l/I等）
         * 2. 單詞邊界錯誤：修正錯誤分割的單詞和連接的單詞
         * 3. 多餘空格：移除單詞內的不必要空格
         * 4. 缺失空格：在適當位置添加空格
         * 5. 標點符號錯誤：修正錯誤識別的標點符號
         *
         * @param text 輸入文本
         * @return 修正後的文本
         */
        private String correctOCRErrors(String text) {
            if (text == null || text.trim().isEmpty()) {
                return text;
            }

            int originalLength = text.length();
            String corrected = text;

            // 0. 特殊符號和噪音過濾（新增）
            corrected = filterSpecialSymbolsAndNoise(corrected);

            // 1. 字符替換錯誤修正
            corrected = correctCharacterSubstitutions(corrected);

            // 2. 單詞邊界錯誤修正
            corrected = correctWordBoundaryErrors(corrected);

            // 3. 標點符號錯誤修正
            corrected = correctPunctuationErrors(corrected);

            // 記錄修正統計
            if (!corrected.equals(text)) {
                int corrections = Math.abs(corrected.length() - originalLength);
                Log.d(TAG, String.format("OCR error correction applied: %d character changes", corrections));
            }

            return corrected;
        }

        /**
         * 修正常見的字符替換錯誤
         * 基於OCR常見混淆字符對進行修正
         *
         * @param text 輸入文本
         * @return 修正字符替換錯誤後的文本
         */
        private String correctCharacterSubstitutions(String text) {
            String corrected = text;
            int corrections = 0;

            // 常見的OCR字符混淆對 - 基於研究文獻和實際經驗
            // 數字與字母混淆
            corrected = applyContextualSubstitution(corrected, "0", "O", true);  // 0 vs O
            corrected = applyContextualSubstitution(corrected, "1", "l", true);  // 1 vs l
            corrected = applyContextualSubstitution(corrected, "1", "I", true);  // 1 vs I
            corrected = applyContextualSubstitution(corrected, "5", "S", true);  // 5 vs S
            corrected = applyContextualSubstitution(corrected, "6", "G", false); // 6 vs G
            corrected = applyContextualSubstitution(corrected, "8", "B", false); // 8 vs B

            // 字母間混淆
            corrected = applyContextualSubstitution(corrected, "rn", "m", false); // rn vs m
            corrected = applyContextualSubstitution(corrected, "cl", "d", false); // cl vs d
            corrected = applyContextualSubstitution(corrected, "vv", "w", false); // vv vs w
            corrected = applyContextualSubstitution(corrected, "ii", "n", false); // ii vs n (某些字體)

            // 中文常見混淆（簡化處理）
            corrected = corrected.replaceAll("囗", "口");  // 囗 vs 口
            corrected = corrected.replaceAll("巳", "己");  // 巳 vs 己

            if (!corrected.equals(text)) {
                corrections = countDifferences(text, corrected);
                Log.d(TAG, String.format("Character substitution corrections: %d", corrections));
            }

            return corrected;
        }

        /**
         * 基於上下文的字符替換
         * 根據周圍字符的類型決定是否進行替換
         *
         * @param text 輸入文本
         * @param from 要替換的字符
         * @param to 替換目標字符
         * @param isDigitLetter 是否為數字字母混淆
         * @return 替換後的文本
         */
        private String applyContextualSubstitution(String text, String from, String to, boolean isDigitLetter) {
            if (!text.contains(from)) {
                return text;
            }

            StringBuilder result = new StringBuilder();
            int i = 0;

            while (i < text.length()) {
                if (i <= text.length() - from.length() && text.substring(i, i + from.length()).equals(from)) {
                    // 檢查上下文
                    char prevChar = i > 0 ? text.charAt(i - 1) : ' ';
                    char nextChar = i + from.length() < text.length() ? text.charAt(i + from.length()) : ' ';

                    boolean shouldReplace = false;

                    if (isDigitLetter) {
                        // 數字字母混淆：根據周圍字符類型決定
                        boolean prevIsDigit = Character.isDigit(prevChar);
                        boolean nextIsDigit = Character.isDigit(nextChar);
                        boolean prevIsLetter = Character.isLetter(prevChar);
                        boolean nextIsLetter = Character.isLetter(nextChar);

                        if (Character.isDigit(from.charAt(0))) {
                            // from是數字，考慮替換為字母
                            shouldReplace = (prevIsLetter || nextIsLetter) && !(prevIsDigit || nextIsDigit);
                        } else {
                            // from是字母，考慮替換為數字
                            shouldReplace = (prevIsDigit || nextIsDigit) && !(prevIsLetter || nextIsLetter);
                        }
                    } else {
                        // 其他字符混淆：簡單的頻率基礎替換
                        shouldReplace = true;
                    }

                    if (shouldReplace) {
                        result.append(to);
                    } else {
                        result.append(from);
                    }
                    i += from.length();
                } else {
                    result.append(text.charAt(i));
                    i++;
                }
            }

            return result.toString();
        }

        /**
         * 修正單詞邊界錯誤
         * 處理錯誤分割的單詞和錯誤連接的單詞
         *
         * @param text 輸入文本
         * @return 修正單詞邊界錯誤後的文本
         */
        private String correctWordBoundaryErrors(String text) {
            String corrected = text;
            int corrections = 0;

            // 1. 修正錯誤分割的單詞（如 "Hel lo" -> "Hello"）
            corrected = fixSplitWords(corrected);

            // 2. 修正連接的單詞（如 "HelloWorld" -> "Hello World"）
            corrected = fixConcatenatedWords(corrected);

            // 3. 移除單詞內的多餘空格
            corrected = removeExtraSpacesInWords(corrected);

            if (!corrected.equals(text)) {
                corrections = countDifferences(text, corrected);
                Log.d(TAG, String.format("Word boundary corrections: %d", corrections));
            }

            return corrected;
        }

        /**
         * 修正錯誤分割的單詞
         * 識別並合併被錯誤分割的單詞
         *
         * @param text 輸入文本
         * @return 修正後的文本
         */
        private String fixSplitWords(String text) {
            // 處理常見的錯誤分割模式
            String corrected = text;

            // 修正短分割（1-2個字符的分割）
            // 例如: "w ord" -> "word", "a nd" -> "and"
            corrected = corrected.replaceAll("\\b([a-zA-Z]{1,2})\\s+([a-zA-Z]{2,})\\b", "$1$2");

            // 修正特定的常見分割錯誤
            // 例如: "hel lo" -> "hello", "wor ld" -> "world"
            corrected = corrected.replaceAll("\\b([a-zA-Z]{2,3})\\s+([a-zA-Z]{2,3})\\b", "$1$2");

            return corrected;
        }

        /**
         * 修正連接的單詞
         * 在適當位置添加空格分離連接的單詞
         *
         * @param text 輸入文本
         * @return 修正後的文本
         */
        private String fixConcatenatedWords(String text) {
            String corrected = text;

            // 在大寫字母前添加空格（駝峰命名法模式）
            // 例如: "HelloWorld" -> "Hello World"
            corrected = corrected.replaceAll("([a-z])([A-Z])", "$1 $2");

            // 在數字和字母之間添加空格
            // 例如: "123abc" -> "123 abc", "abc123" -> "abc 123"
            corrected = corrected.replaceAll("(\\d)([a-zA-Z])", "$1 $2");
            corrected = corrected.replaceAll("([a-zA-Z])(\\d)", "$1 $2");

            // 中英文混合：在中文和英文之間添加空格
            corrected = corrected.replaceAll("([\\u4e00-\\u9fff])([a-zA-Z])", "$1 $2");
            corrected = corrected.replaceAll("([a-zA-Z])([\\u4e00-\\u9fff])", "$1 $2");

            return corrected;
        }

        /**
         * 移除單詞內的多餘空格
         * 清理單詞內部的不必要空格
         *
         * @param text 輸入文本
         * @return 清理後的文本
         */
        private String removeExtraSpacesInWords(String text) {
            // 移除單個字母後的空格（如果後面跟著小寫字母）
            // 例如: "t he" -> "the", "a nd" -> "and"
            return text.replaceAll("\\b([a-zA-Z])\\s+([a-z]{2,})\\b", "$1$2");
        }

        /**
         * 修正標點符號錯誤
         * 修正常見的標點符號識別錯誤
         *
         * @param text 輸入文本
         * @return 修正標點符號錯誤後的文本
         */
        private String correctPunctuationErrors(String text) {
            String corrected = text;
            int corrections = 0;

            // 常見標點符號混淆修正
            corrected = corrected.replaceAll("\\s*,\\s*", ", ");     // 逗號空格標準化
            corrected = corrected.replaceAll("\\s*\\.\\s*", ". ");   // 句號空格標準化
            corrected = corrected.replaceAll("\\s*;\\s*", "; ");     // 分號空格標準化
            corrected = corrected.replaceAll("\\s*:\\s*", ": ");     // 冒號空格標準化

            // 修正常見的標點符號替換錯誤
            corrected = corrected.replaceAll("''", "\"");             // 兩個單引號 -> 雙引號
            corrected = corrected.replaceAll("``", "\"");             // 兩個反引號 -> 雙引號
            corrected = corrected.replaceAll("'([^']*)'", "\"$1\"");  // 單引號包圍 -> 雙引號包圍

            // 修正問號和感嘆號前的空格
            corrected = corrected.replaceAll("\\s+([?!])", "$1");

            // 修正括號周圍的空格
            corrected = corrected.replaceAll("\\(\\s+", "(");
            corrected = corrected.replaceAll("\\s+\\)", ")");

            // 中文標點符號修正
            corrected = corrected.replaceAll("，\\s+", "，");         // 中文逗號後無空格
            corrected = corrected.replaceAll("。\\s+", "。");         // 中文句號後無空格
            corrected = corrected.replaceAll("；\\s+", "；");         // 中文分號後無空格
            corrected = corrected.replaceAll("：\\s+", "：");         // 中文冒號後無空格

            if (!corrected.equals(text)) {
                corrections = countDifferences(text, corrected);
                Log.d(TAG, String.format("Punctuation corrections: %d", corrections));
            }

            return corrected;
        }

        /**
         * 計算兩個字符串之間的差異數量
         * 用於統計修正的數量
         *
         * @param original 原始字符串
         * @param corrected 修正後的字符串
         * @return 差異數量
         */
        private int countDifferences(String original, String corrected) {
            if (original == null || corrected == null) {
                return 0;
            }

            int differences = 0;
            int minLength = Math.min(original.length(), corrected.length());

            // 計算字符級別的差異
            for (int i = 0; i < minLength; i++) {
                if (original.charAt(i) != corrected.charAt(i)) {
                    differences++;
                }
            }

            // 添加長度差異
            differences += Math.abs(original.length() - corrected.length());

            return differences;
        }

        
        /**
         * 通知結果（記憶體安全）
         */
        private void notifyResult(String result, boolean isProcessing) {
            mainThreadHandler.post(() -> {
                OCRCallback callback = callbackRef.get();
                if (callback != null) {
                    callback.onProcessComplete(result);
                    callback.onisProcessingChanged(isProcessing);
                } else {
                    Log.w(TAG, "OCRCallback has been garbage collected");
                }
            });
        }
    }
    
    /**
     * 獲取性能統計
     */
    public String getPerformanceStats() {
        int total = totalRequests.get();
        int success = successfulRequests.get();
        int failed = failedRequests.get();
        
        double successRate = total > 0 ? (double) success / total * 100 : 0;
        
        return String.format("OCR Performance - Total: %d, Success: %d (%.1f%%), Failed: %d", 
            total, success, successRate, failed);
    }
    
    /**
     * 重置統計數據
     */
    public void resetStats() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        Log.i(TAG, "Performance statistics reset");
    }
    
    /**
     * 清理資源
     */
    public void destroy() {
        Log.i(TAG, "Destroying GoogleOCR instance");
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 清理Vision客戶端
        synchronized (visionClientLock) {
            visionClient = null;
        }

        Log.i(TAG, getPerformanceStats());
        Log.i(TAG, "GoogleOCR destroyed successfully");
    }

    /**
     * 過濾特殊符號和噪音內容
     *
     * 處理OCR輸出中的無意義內容：
     * - 移除無法朗讀的特殊符號
     * - 過濾鍵盤序列和HTML片段
     * - 移除質量過低的行
     * - 保留有意義的中英文內容
     *
     * @param text 輸入文本
     * @return 過濾後的文本
     */
    private String filterSpecialSymbolsAndNoise(String text) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        StringBuilder filtered = new StringBuilder();
        String[] lines = text.split("\n");
        int originalLines = lines.length;
        int filteredLines = 0;

        for (String line : lines) {
            String cleanedLine = cleanLine(line);
            if (isValidLine(cleanedLine)) {
                if (filtered.length() > 0) {
                    filtered.append("\n");
                }
                filtered.append(cleanedLine);
                filteredLines++;
            }
        }

        // 記錄過濾統計
        if (filteredLines < originalLines) {
            Log.d(TAG, String.format("Noise filtering: %d/%d lines kept (%.1f%%)",
                filteredLines, originalLines, (filteredLines * 100.0 / originalLines)));
        }

        return filtered.toString();
    }

    /**
     * 清理單行文本，移除特殊符號
     */
    private String cleanLine(String line) {
        if (line == null) return "";

        // 移除常見的OCR噪音符號
        String cleaned = line
            // 移除箭頭和特殊符號
            .replaceAll("[↑↓←→↖↗↘↙⇑⇓⇐⇒⇖⇗⇘⇙]", "")
            // 移除注音符號
            .replaceAll("[ˊˇˋ˙ㄅ-ㄩ]", "")
            // 移除特殊標點和符號
            .replaceAll("[◆◇◈◉◎●○◐◑◒◓◔◕◖◗◘◙◚◛◜◝◞◟◠◡◢◣◤◥◦◧◨◩◪◫◬◭◮◯]", "")
            // 移除數學符號
            .replaceAll("[±×÷≠≤≥≈∞∑∏∫∂∇∆∴∵∈∉∪∩⊂⊃⊆⊇⊕⊗⊙⊥∥∠∟]", "")
            // 移除貨幣和單位符號（保留基本的）
            .replaceAll("[¢£¤¥¦§¨©ª«¬®¯°±²³´µ¶·¸¹º»¼½¾¿]", "")
            // 移除框線字符
            .replaceAll("[┌┐└┘├┤┬┴┼─│┏┓┗┛┣┫┳┻╋━┃]", "")
            // 移除重複的空格
            .replaceAll("\\s+", " ")
            .trim();

        return cleaned;
    }

    /**
     * 判斷行是否為有效內容
     */
    private boolean isValidLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return false;
        }

        line = line.trim();

        // 過短的行（少於2個字符），但保留有意義的中文單字
        if (line.length() < 2) {
            // 如果是單個中文字符，可能有意義，檢查是否為常見的有意義字符
            if (line.length() == 1) {
                char c = line.charAt(0);
                if (isChineseChar(c)) {
                    // 保留一些有意義的中文單字，如"月"、"日"、"年"等
                    String meaningfulSingleChars = "月日年時分秒元件頁集章節部分類型方式";
                    return meaningfulSingleChars.indexOf(c) >= 0;
                }
            }
            return false;
        }

        // 檢查是否為功能鍵序列
        if (isFunctionKeySequence(line)) {
            return false;
        }

        // 檢查是否為鍵盤序列
        if (containsKeyboardSequence(line)) {
            return false;
        }

        // 檢查是否為HTML/URL片段
        if (isHtmlOrUrlFragment(line)) {
            return false;
        }

        // 檢查是否為純符號或數字組合
        if (isPureSymbolOrNumberSequence(line)) {
            return false;
        }

        // 計算有效字符比例
        double validRatio = calculateValidCharRatio(line);

        // 如果有效字符比例太低，過濾掉
        return validRatio >= 0.6; // 至少60%的字符是有意義的
    }

    /**
     * 檢測是否為功能鍵序列
     */
    private boolean isFunctionKeySequence(String text) {
        String trimmed = text.trim().toLowerCase();

        // 功能鍵模式：F + 數字
        if (trimmed.matches("f\\s*\\d+")) {
            return true;
        }

        // 其他常見的功能鍵組合
        String[] functionKeys = {"ctrl", "alt", "shift", "tab", "enter", "space", "delete", "backspace"};
        for (String key : functionKeys) {
            if (trimmed.equals(key) || trimmed.startsWith(key + " ") || trimmed.endsWith(" " + key)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 檢測是否為純符號或數字組合
     */
    private boolean isPureSymbolOrNumberSequence(String text) {
        String trimmed = text.trim();

        // 純數字（單個數字或簡單數字組合）
        if (trimmed.matches("^\\d+$") && trimmed.length() <= 2) {
            return true;
        }

        // 純符號
        if (trimmed.matches("^[\\p{Punct}\\p{Symbol}]+$")) {
            return true;
        }

        // 數字+符號的簡單組合
        if (trimmed.matches("^[\\d\\p{Punct}\\p{Symbol}\\s]+$") && trimmed.length() <= 5) {
            return true;
        }

        return false;
    }

    /**
     * 檢測是否包含鍵盤序列
     */
    private boolean containsKeyboardSequence(String text) {
        // 常見的鍵盤序列模式
        String[] keyboardPatterns = {
            "qwerty", "asdf", "zxcv", "uiop", "hjkl", "bnm",
            "123456", "789", "abc", "def", "ghi", "jkl", "mno", "pqr", "stu", "vwx"
        };

        String lowerText = text.toLowerCase().replaceAll("\\s+", "");

        for (String pattern : keyboardPatterns) {
            if (lowerText.contains(pattern) && lowerText.length() <= pattern.length() + 3) {
                return true;
            }
        }

        // 檢測連續的字母序列（如 "E RTY U"）
        String noSpaces = text.replaceAll("\\s+", "").toLowerCase();
        if (noSpaces.matches(".*[a-z]{4,}.*") && text.length() < 15) {
            return true;
        }

        // 特別檢測 "E RTY U" 這種帶空格的鍵盤序列
        if (text.toLowerCase().matches(".*[a-z]\\s+[a-z]\\s+[a-z]\\s+[a-z].*")) {
            return true;
        }

        return false;
    }

    /**
     * 檢測是否為HTML或URL片段
     */
    private boolean isHtmlOrUrlFragment(String text) {
        // HTML標籤模式
        if (text.matches(".*<[^>]+>.*") || text.matches(".*</[^>]+>.*")) {
            return true;
        }

        // URL模式
        if (text.matches(".*(http|www|html|php|asp|jsp).*")) {
            return true;
        }

        // 文件路徑模式
        if (text.matches(".*[A-Z]:/.*") || text.matches(".*/[^/]+\\.[a-z]{2,4}.*")) {
            return true;
        }

        return false;
    }

    /**
     * 計算有效字符比例
     */
    private double calculateValidCharRatio(String text) {
        if (text == null || text.isEmpty()) {
            return 0.0;
        }

        int totalChars = 0;
        int validChars = 0;

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            totalChars++;

            // 有效字符：中文、英文、數字、基本標點
            if (Character.isLetterOrDigit(c) ||
                isChineseChar(c) ||
                isBasicPunctuation(c) ||
                Character.isWhitespace(c)) {
                validChars++;
            }
        }

        return totalChars > 0 ? (double) validChars / totalChars : 0.0;
    }

    /**
     * 判斷是否為中文字符
     */
    private boolean isChineseChar(char c) {
        return c >= '\u4e00' && c <= '\u9fff';
    }

    /**
     * 判斷是否為基本標點符號
     */
    private boolean isBasicPunctuation(char c) {
        String basicPunctuation = "。，！？：；\"\"''（）【】《》〈〉「」『』.,!?:;\"'()[]<>";
        return basicPunctuation.indexOf(c) >= 0;
    }

    /**
     * 選擇性OCR錯誤修正 - 保留有用功能，禁用過度字符替換
     *
     * 保留的功能：
     * 1. ✅ 特殊符號和噪音過濾
     * 2. ✅ 單詞邊界錯誤修正
     * 3. ✅ 標點符號錯誤修正
     *
     * 禁用的功能：
     * 1. ❌ 字符替換（8→B, 6→G, 0→O等）
     * 2. ❌ 過度的上下文判斷
     *
     * @param text 輸入文本
     * @return 選擇性修正後的文本
     */
    private String correctOCRErrorsSelective(String text) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        int originalLength = text.length();
        String corrected = text;

        // 1. ✅ 特殊符號和噪音過濾（保留）
        corrected = filterSpecialSymbolsAndNoise(corrected);

        // 2. ❌ 字符替換錯誤修正（禁用）
        // corrected = correctCharacterSubstitutions(corrected);

        // 3. ✅ 單詞邊界錯誤修正（簡化實現）
        corrected = correctWordBoundaryErrorsSimple(corrected);

        // 4. ✅ 標點符號錯誤修正（簡化實現）
        corrected = correctPunctuationErrorsSimple(corrected);

        // 5. ✅ 基本空白字符清理（保留）
        corrected = corrected.replaceAll("\\r\\n", "\n");
        corrected = corrected.replaceAll("\\r", "\n");
        corrected = corrected.replaceAll("  +", " ");

        // 記錄修正統計
        int finalLength = corrected.length();
        if (finalLength != originalLength) {
            Log.d(TAG, String.format("Selective OCR corrections applied: %d → %d chars (%.1f%% change)",
                originalLength, finalLength, ((finalLength - originalLength) * 100.0 / originalLength)));
        }

        return corrected;
    }

    /**
     * 簡化的單詞邊界錯誤修正
     */
    private String correctWordBoundaryErrorsSimple(String text) {
        String corrected = text;

        // 修正短分割（1-2個字符的分割）
        // 例如: "w ord" -> "word", "a nd" -> "and"
        corrected = corrected.replaceAll("\\b([a-zA-Z]{1,2})\\s+([a-zA-Z]{2,})\\b", "$1$2");

        // 修正常見的分割錯誤
        corrected = corrected.replaceAll("\\b([a-zA-Z]{2,})\\s+([a-zA-Z]{1,3})\\b", "$1$2");

        // 在大寫字母前添加空格（修正連接錯誤）
        corrected = corrected.replaceAll("([a-z])([A-Z])", "$1 $2");

        return corrected;
    }

    /**
     * 簡化的標點符號錯誤修正
     */
    private String correctPunctuationErrorsSimple(String text) {
        String corrected = text;

        // 常見標點符號空格標準化
        corrected = corrected.replaceAll("\\s*,\\s*", ", ");     // 逗號
        corrected = corrected.replaceAll("\\s*\\.\\s*", ". ");   // 句號
        corrected = corrected.replaceAll("\\s*;\\s*", "; ");     // 分號
        corrected = corrected.replaceAll("\\s*:\\s*", ": ");     // 冒號

        // 修正問號和感嘆號前的空格
        corrected = corrected.replaceAll("\\s+([?!])", "$1");

        // 修正括號周圍的空格
        corrected = corrected.replaceAll("\\(\\s+", "(");
        corrected = corrected.replaceAll("\\s+\\)", ")");

        // 中文標點符號修正
        corrected = corrected.replaceAll("，\\s+", "，");         // 中文逗號後無空格
        corrected = corrected.replaceAll("。\\s+", "。");         // 中文句號後無空格
        corrected = corrected.replaceAll("；\\s+", "；");         // 中文分號後無空格
        corrected = corrected.replaceAll("：\\s+", "：");         // 中文冒號後無空格

        return corrected;
    }

    /**
     * 保守的OCR錯誤修正 - 只修正明顯的錯誤，避免過度處理
     *
     * 原則：
     * 1. 只修正明顯的OCR錯誤
     * 2. 不修改數字和字母的組合（如網址、代碼等）
     * 3. 保留原始內容的完整性
     * 4. 只處理空白字符和明顯的標點錯誤
     *
     * @param text 輸入文本
     * @return 保守修正後的文本
     */
    private String correctOCRErrorsConservative(String text) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        String corrected = text;

        // 1. 只修正明顯的空白字符問題
        // 移除行首行尾多餘空格
        corrected = corrected.replaceAll("(?m)^\\s+", "");
        corrected = corrected.replaceAll("(?m)\\s+$", "");

        // 2. 修正明顯的標點符號錯誤（非常保守）
        // 只修正明顯錯誤的省略號
        corrected = corrected.replaceAll("\\.\\.\\.", "…");

        // 3. 修正明顯的重複空格
        corrected = corrected.replaceAll("  +", " ");

        // 4. 修正明顯的換行問題
        corrected = corrected.replaceAll("\\r\\n", "\n");
        corrected = corrected.replaceAll("\\r", "\n");

        // 記錄修正情況
        if (!corrected.equals(text)) {
            Log.d(TAG, "Applied conservative OCR corrections (whitespace and punctuation only)");
        }

        return corrected;
    }
}