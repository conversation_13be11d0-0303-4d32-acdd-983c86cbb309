<?xml version="1.0" encoding="utf-8"?>
<!-- 關鍵改動 1: 移除已過時的 package 屬性 -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!-- 為了相容 Android 10 (API 29) 以下的裝置，保留 WRITE_EXTERNAL_STORAGE -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="29" />
    <!-- 其他權限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:ignore="ScopedStorage" />


    <!-- 關鍵改動 2: 從 application 標籤中移除 extractNativeLibs -->
    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/app_icon"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/app_icon"
        android:supportsRtl="true"
        android:theme="@style/Theme.CameraSDK"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <activity
            android:name=".view.SplashActivity"
            android:exported="true"> <!-- 關鍵改動 3: 啟動 Activity 必須設為 exported="true" -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".view.TestActivity"
            android:exported="false" /> <!-- 這個 Activity 由 App 內部啟動，設為 false 更安全 -->
        <activity
            android:name=".view.SettingActivity"
            android:exported="false" /> <!-- 這個 Activity 由 App 內部啟動，設為 false 更安全 -->

        <service
            android:name=".utils.TalkbackAudio"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="true"> <!-- 關鍵改動 4: 無障礙服務必須設為 exported="true" -->
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

<!--        <receiver-->
<!--            android:name=".utils.VolumeChangeReceiver"-->
<!--            android:enabled="true"-->
<!--            android:exported="true"> &lt;!&ndash; 關鍵改動 5: 接收系統廣播的 Receiver 必須設為 exported="true" &ndash;&gt;-->
<!--            <intent-filter>-->
<!--                <action android:name="android.media.VOLUME_CHANGED_ACTION" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

    </application>

</manifest>
