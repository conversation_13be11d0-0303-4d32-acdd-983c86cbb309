<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_enabled="false">
        <shape android:shape="rectangle">
            <corners android:radius="5dip"/>
            <solid android:color="@color/white"/>
            <stroke
                android:width="1dip"
                android:color="@color/white" />
        </shape>
    </item>
    <item
        android:state_focused="false"
        android:state_pressed="false">
        <shape android:shape="rectangle">
            <corners android:radius="5dip"/>
            <solid android:color="@color/white"/>
            <stroke
                android:width="1dip"
                android:color="@color/white" />
        </shape>
    </item>
    <item
        android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="5dip"/>
            <solid android:color="#FF333377"/>
            <stroke
                android:width="1dip"
                android:color="#FF333377" />
        </shape>
    </item>
    <item
        android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="5dip"/>
            <solid android:color="#FF333377"/>
            <stroke
                android:width="1dip"
                android:color="#FF333377" />
        </shape>
    </item>

</selector>