<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:id="@+id/cameraViewContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" />

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:orientation="vertical"-->
<!--        android:gravity="center">-->

<!--        <com.xy.demo.view.AutoSizePreview-->
<!--            android:id="@+id/surfaceview"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:rotation="270" />-->

<!--    </LinearLayout>-->

    <ScrollView
        android:visibility="gone"
        android:id="@+id/textviewBorder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="100dp"
        android:background="@drawable/textview_border"
        android:padding="20dp"
        android:scrollbars="none"
        android:importantForAccessibility="noHideDescendants">
        <TextView
            android:id="@+id/resultTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/result"
            android:textSize="16sp"
            android:importantForAccessibility="noHideDescendants"
            />
    </ScrollView>


    <!--
        <com.xy.usb.api.MyTextureView
            android:id="@+id/texture_view"
            android:layout_width="match_parent"
            android:layout_height="270dp">
        </com.xy.usb.api.MyTextureView>
    -->

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="vertical"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        >-->
<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:orientation="horizontal"-->
<!--            >-->
<!--            <Button-->
<!--                android:id="@+id/takephoto"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginLeft="3dp"-->
<!--                android:text="拍照"-->
<!--                />-->
<!--            <Button-->
<!--                android:id="@+id/previewstart"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginLeft="3dp"-->
<!--                android:text="开预览帧"-->
<!--                />-->

<!--            <Button-->
<!--                android:id="@+id/previewstop"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginLeft="3dp"-->
<!--                android:text="关预览帧"-->
<!--                />-->
<!--        </LinearLayout>-->
<!--    </LinearLayout>-->


    <LinearLayout
        android:id="@+id/buttonsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="5dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="8dp"
            >

            <ImageButton
                android:id="@+id/ocrBtn"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/circle_button"
                android:src="@drawable/baseline_photo_camera_24"
                android:contentDescription="@string/capture_the_image"
                android:text="shoot"
                />

            <ImageButton
                android:visibility="gone"
                android:id="@+id/stopBtn"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/circle_button"
                android:src="@drawable/baseline_pause_24"
                android:contentDescription="@string/play_or_stop" />

            <ImageButton
                android:visibility="gone"
                android:id="@+id/replayBtn"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/circle_button"
                android:src="@drawable/baseline_replay_24"
                android:rotation="60"
                android:scaleX="-1"
                android:contentDescription="@string/replay_the_result"
                android:text="Replay" />

            <ImageButton
                android:id="@+id/settingBtn"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/circle_button"
                android:src="@drawable/outline_settings_24"
                android:contentDescription="@string/go_to_setting_page"
                android:text="setting" />

            <ImageButton
                android:visibility="gone"
                android:id="@+id/backBtn"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/circle_button"
                android:src="@drawable/round_arrow_back_24"
                android:contentDescription="@string/go_back_ocr_page"
                android:text="return" />
        </LinearLayout>
    </RelativeLayout>
