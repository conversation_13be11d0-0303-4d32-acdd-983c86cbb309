<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/settingTV"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/setting_lan"
        android:paddingTop="8dp"
        android:gravity="center"
        android:textSize="24sp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"/>

    <ScrollView
        android:id="@+id/settingSV"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/settingTV"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="48dp"
        android:fillViewport="true">

        <LinearLayout
            android:id="@+id/settingLL"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/languageButtonsLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/border_shape"
                android:orientation="vertical">
                <TextView
                    android:id="@+id/languageLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/select_language"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:textSize="24sp"
                    />
                <Button
                    android:id="@+id/chineseButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/chinese_language"
                    android:tag="zh"
                    android:padding="5dp"
                    style="@style/SelectionButton"
                    />
                <Button
                    android:id="@+id/englishButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:tag="en"
                    android:text="@string/english_language"
                    android:padding="5dp"
                    style="@style/SelectionButton"
                    android:background="@drawable/selected_button_style"
                    />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/speedButtonsLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/border_shape"
                android:orientation="vertical"
                android:padding="10dp">

                <TextView
                    android:id="@+id/textSpeedLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:text="@string/select_text_speed"
                    android:textSize="24sp" />

                <LinearLayout
                    android:layout_width="303dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="horizontal">

                    <ImageButton
                        android:id="@+id/slowButton"
                        style="@style/SelectionButton"
                        android:layout_width="72dp"
                        android:layout_height="84dp"
                        android:layout_weight="1"
                        android:contentDescription="@string/slow_speed"
                        android:padding="5dp"
                        android:scaleType="fitXY"
                        android:src="@drawable/ic_baseline_remove_24"
                        android:tag="Slow"
                        android:text="@string/slow_speed">

                    </ImageButton>


                    <TextView
                        android:id="@+id/speedTextTV"
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center_horizontal"
                        android:padding="5dp"
                        android:text="1"
                        android:textSize="48sp" />

                    <ImageButton
                        android:id="@+id/fastButton"
                        style="@style/SelectionButton"
                        android:layout_width="72dp"
                        android:layout_height="84dp"
                        android:layout_weight="1"
                        android:contentDescription="@string/fast_speed"
                        android:padding="5dp"
                        android:scaleType="fitXY"
                        android:src="@drawable/ic_baseline_add_24"
                        android:tag="Fast"
                        android:text="@string/fast_speed" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/outPutLanButtonsLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:padding="16dp"
                android:background="@drawable/border_shape"
                android:orientation="vertical">

                <!-- Existing TextView -->
                <TextView
                    android:id="@+id/lan"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/select_voice_language"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:textSize="24sp" />

                <!-- Existing Button for Cantonese -->
                <Button
                    android:id="@+id/voiceCantoneseButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/voice_cantonese"
                    android:tag="Cantonese"
                    android:padding="5dp"
                    style="@style/SelectionButton" />

                <!-- Existing Button for Mandarin -->
                <Button
                    android:id="@+id/voiceMandarinButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/voice_mandarin"
                    android:tag="Mandarin"
                    android:padding="5dp"
                    style="@style/SelectionButton" />

                <!-- New Button for English -->
                <Button
                    android:id="@+id/voiceEnglishButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/voice_english"
                    android:tag="English"
                    android:padding="5dp"
                    style="@style/SelectionButton" />

            </LinearLayout>

        </LinearLayout>


    </ScrollView>

    <Button
        android:id="@+id/backButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="20dp"
        android:text="@string/back"
        style="@style/SecondaryActionButton" />



</RelativeLayout>
