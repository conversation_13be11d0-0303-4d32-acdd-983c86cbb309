<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".view.TestActivity">

    <!-- 層級 1: 相機預覽，作為最底層背景 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/cameraViewContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical" />

    <!-- 層級 2: UI 控制項 -->

    <!-- 底部控制項的容器和錨點 -->
    <FrameLayout
        android:id="@+id/bottomControlsAnchor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:paddingTop="20dp"
        android:paddingBottom="30dp">

        <!-- 拍照模式下的按鈕群組 -->
        <LinearLayout
            android:id="@+id/captureControls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="visible">

            <!-- 佔位符，用於將拍照按鈕和設定按鈕均勻分佈 -->
            <Space
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginEnd="20dp" />

            <!-- 底部中央的拍照按鈕 -->
            <ImageButton
                android:id="@+id/ocrBtn"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:background="@drawable/round_button"
                android:padding="20dp"
                android:scaleType="fitCenter"
                android:src="@drawable/baseline_photo_camera_24" />

            <!-- 新增：設定按鈕移至拍照按鈕旁邊 -->
            <ImageButton
                android:id="@+id/settingBtn"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginStart="20dp"
                android:background="@drawable/circle_button"
                android:src="@drawable/outline_settings_24"
                app:tint="@android:color/white" />

        </LinearLayout>

        <!-- OCR 流程中的控制按鈕群組 -->
        <LinearLayout
            android:id="@+id/playbackControls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageButton
                android:id="@+id/backBtn"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/circle_button"
                android:src="@drawable/round_arrow_back_24"
                app:tint="@android:color/white" />

            <ImageButton
                android:id="@+id/stopBtn"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:background="@drawable/round_button"
                android:src="@drawable/baseline_pause_24"
                app:tint="@android:color/white" />

            <ImageButton
                android:id="@+id/replayBtn"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginStart="20dp"
                android:background="@drawable/circle_button"
                android:src="@drawable/round_replay_24"
                app:tint="@android:color/white" />
        </LinearLayout>
    </FrameLayout>

    <!-- 讓 ScrollView 填滿頂部和底部錨點之間的空間 -->
    <ScrollView
        android:id="@+id/textviewBorder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/bottomControlsAnchor"
        android:layout_alignParentTop="true"
        android:layout_margin="20dp"
    android:background="#80000000"
    android:padding="8dp"
    android:visibility="gone"
    tools:visibility="visible">

    <TextView
        android:id="@+id/resultTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
    android:textColor="@android:color/white"
    android:textSize="16sp"
    tools:text="這裡是 OCR 識別結果..." />
</ScrollView>

    </RelativeLayout>
