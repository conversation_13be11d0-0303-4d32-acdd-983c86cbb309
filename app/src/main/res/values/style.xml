<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="MyTranslucentTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>

    <style name="SelectionButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textSize">28sp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>

    <style name="SelectedButton">

    </style>

    <style name="ActionButton" parent="SelectionButton">
        <item name="android:background">@drawable/action_button_background</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="SecondaryActionButton" parent="ActionButton">
        <item name="android:background">@drawable/secondary_action_button_background</item>
    </style>


</resources>
