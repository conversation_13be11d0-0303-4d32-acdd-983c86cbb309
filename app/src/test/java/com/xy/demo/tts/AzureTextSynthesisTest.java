package com.xy.demo.tts;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;
import java.lang.reflect.Method;

/**
 * 测试 AzureTextSynthesis 的语言检测重构
 */
public class AzureTextSynthesisTest {
    
    private AzureTextSynthesis azureTTS;
    
    @Before
    public void setUp() {
        // 创建一个测试实例（不需要真实的Context和SpeechConfig）
        azureTTS = new AzureTextSynthesis(null, null);
    }
    
    @Test
    public void testCountHanChars() throws Exception {
        // 使用反射访问私有方法
        Method countHanChars = AzureTextSynthesis.class.getDeclaredMethod("countHanChars", String.class);
        countHanChars.setAccessible(true);
        
        // 测试纯中文
        int result1 = (Integer) countHanChars.invoke(azureTTS, "你好世界");
        assertEquals(4, result1);
        
        // 测试混合文本
        int result2 = (Integer) countHanChars.invoke(azureTTS, "Hello 世界 123");
        assertEquals(2, result2);
        
        // 测试空文本
        int result3 = (Integer) countHanChars.invoke(azureTTS, "");
        assertEquals(0, result3);
        
        // 测试null
        int result4 = (Integer) countHanChars.invoke(azureTTS, (String) null);
        assertEquals(0, result4);
    }
    
    @Test
    public void testCountLatinChars() throws Exception {
        Method countLatinChars = AzureTextSynthesis.class.getDeclaredMethod("countLatinChars", String.class);
        countLatinChars.setAccessible(true);
        
        // 测试纯英文
        int result1 = (Integer) countLatinChars.invoke(azureTTS, "Hello World");
        assertEquals(10, result1); // 不包括空格
        
        // 测试混合文本
        int result2 = (Integer) countLatinChars.invoke(azureTTS, "Hello 世界 123");
        assertEquals(5, result2); // 只计算 Hello
        
        // 测试带重音符号的拉丁字符
        int result3 = (Integer) countLatinChars.invoke(azureTTS, "café résumé");
        assertEquals(10, result3); // 包括 é
    }
    
    @Test
    public void testDetectPrimaryLanguage() throws Exception {
        Method detectPrimaryLanguage = AzureTextSynthesis.class.getDeclaredMethod("detectPrimaryLanguage", String.class);
        detectPrimaryLanguage.setAccessible(true);
        
        // 测试中文为主
        String result1 = (String) detectPrimaryLanguage.invoke(azureTTS, "你好世界Hello");
        assertEquals("chinese", result1);
        
        // 测试英文为主
        String result2 = (String) detectPrimaryLanguage.invoke(azureTTS, "Hello World你好");
        assertEquals("english", result2);
        
        // 测试空文本
        String result3 = (String) detectPrimaryLanguage.invoke(azureTTS, "");
        assertEquals("chinese", result3); // 默认中文
    }
    
    @Test
    public void testNewlinePreservationInSSML() throws Exception {
        // 测试换行符是否被正确处理为SSML停顿
        Method addSSMLEnhancements = AzureTextSynthesis.class.getDeclaredMethod("addSSMLEnhancements", String.class, String.class);
        addSSMLEnhancements.setAccessible(true);
        
        String textWithNewlines = "第一行\n第二行\n第三行";
        String result = (String) addSSMLEnhancements.invoke(azureTTS, textWithNewlines, "chinese");
        
        // 验证结果包含break标签
        assertTrue("SSML should contain break tags for newlines", result.contains("<break time="));
        
        // 验证有两个break标签（三行文本应该有两个停顿）
        int breakCount = result.split("<break time=").length - 1;
        assertEquals("Should have 2 break tags for 3 lines", 2, breakCount);
    }
}
