package com.xy.demo.ocr;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;
import java.lang.reflect.Method;

/**
 * 測試OCR錯誤修正功能
 * 驗證各種OCR錯誤類型的修正效果
 */
public class OCRErrorCorrectionTest {
    
    private GoogleOCR googleOCR;
    private Method correctOCRErrorsMethod;
    private Method correctCharacterSubstitutionsMethod;
    private Method correctWordBoundaryErrorsMethod;
    private Method correctPunctuationErrorsMethod;
    
    @Before
    public void setUp() throws Exception {
        googleOCR = new GoogleOCR();
        
        // 使用反射訪問私有方法進行測試
        correctOCRErrorsMethod = GoogleOCR.class.getDeclaredMethod("correctOCRErrors", String.class);
        correctOCRErrorsMethod.setAccessible(true);
        
        correctCharacterSubstitutionsMethod = GoogleOCR.class.getDeclaredMethod("correctCharacterSubstitutions", String.class);
        correctCharacterSubstitutionsMethod.setAccessible(true);
        
        correctWordBoundaryErrorsMethod = GoogleOCR.class.getDeclaredMethod("correctWordBoundaryErrors", String.class);
        correctWordBoundaryErrorsMethod.setAccessible(true);
        
        correctPunctuationErrorsMethod = GoogleOCR.class.getDeclaredMethod("correctPunctuationErrors", String.class);
        correctPunctuationErrorsMethod.setAccessible(true);
    }
    
    @Test
    public void testCharacterSubstitutionCorrection() throws Exception {
        // 測試數字字母混淆修正
        String input1 = "He11o W0rld";
        String result1 = (String) correctCharacterSubstitutionsMethod.invoke(googleOCR, input1);
        assertEquals("Hello World", result1);
        
        // 測試字母間混淆修正
        String input2 = "The quick brovvn fox";
        String result2 = (String) correctCharacterSubstitutionsMethod.invoke(googleOCR, input2);
        assertEquals("The quick brown fox", result2);
        
        // 測試中文字符修正
        String input3 = "這是一個囗試";
        String result3 = (String) correctCharacterSubstitutionsMethod.invoke(googleOCR, input3);
        assertEquals("這是一個口試", result3);
    }
    
    @Test
    public void testWordBoundaryErrorCorrection() throws Exception {
        // 測試錯誤分割的單詞修正
        String input1 = "Hel lo wor ld";
        String result1 = (String) correctWordBoundaryErrorsMethod.invoke(googleOCR, input1);
        assertEquals("Hello world", result1);
        
        // 測試連接單詞的分離
        String input2 = "HelloWorld TestCase";
        String result2 = (String) correctWordBoundaryErrorsMethod.invoke(googleOCR, input2);
        assertEquals("Hello World Test Case", result2);
        
        // 測試數字字母分離
        String input3 = "Test123Case abc456def";
        String result3 = (String) correctWordBoundaryErrorsMethod.invoke(googleOCR, input3);
        assertEquals("Test 123 Case abc 456 def", result3);
        
        // 測試中英文分離
        String input4 = "這是test測試case";
        String result4 = (String) correctWordBoundaryErrorsMethod.invoke(googleOCR, input4);
        assertEquals("這是 test 測試 case", result4);
    }
    
    @Test
    public void testPunctuationErrorCorrection() throws Exception {
        // 測試標點符號空格標準化
        String input1 = "Hello,world.How are you?";
        String result1 = (String) correctPunctuationErrorsMethod.invoke(googleOCR, input1);
        assertEquals("Hello, world. How are you?", result1);
        
        // 測試引號修正
        String input2 = "He said ''Hello world''";
        String result2 = (String) correctPunctuationErrorsMethod.invoke(googleOCR, input2);
        assertEquals("He said \"Hello world\"", result2);
        
        // 測試括號空格修正
        String input3 = "This is ( a test )";
        String result3 = (String) correctPunctuationErrorsMethod.invoke(googleOCR, input3);
        assertEquals("This is (a test)", result3);
        
        // 測試中文標點符號
        String input4 = "你好，世界。這是測試；";
        String result4 = (String) correctPunctuationErrorsMethod.invoke(googleOCR, input4);
        assertEquals("你好，世界。這是測試；", result4);
    }
    
    @Test
    public void testCompleteOCRErrorCorrection() throws Exception {
        // 測試綜合錯誤修正
        String input = "He11o W0r1d,h0w are y0u?This is a t est.";
        String result = (String) correctOCRErrorsMethod.invoke(googleOCR, input);
        
        // 驗證修正結果包含預期的改進
        assertTrue("Should correct character substitutions", result.contains("Hello"));
        assertTrue("Should correct word boundaries", result.contains("test"));
        assertTrue("Should correct punctuation", result.contains(", "));
        assertTrue("Should correct punctuation", result.contains(". "));
    }
    
    @Test
    public void testNullAndEmptyInput() throws Exception {
        // 測試空值處理
        String result1 = (String) correctOCRErrorsMethod.invoke(googleOCR, (String) null);
        assertNull("Should handle null input", result1);
        
        String result2 = (String) correctOCRErrorsMethod.invoke(googleOCR, "");
        assertEquals("Should handle empty input", "", result2);
        
        String result3 = (String) correctOCRErrorsMethod.invoke(googleOCR, "   ");
        assertEquals("Should handle whitespace input", "   ", result3);
    }
    
    @Test
    public void testPreserveOriginalMeaning() throws Exception {
        // 測試保持原始意義
        String input = "The quick brown fox jumps over the lazy dog.";
        String result = (String) correctOCRErrorsMethod.invoke(googleOCR, input);
        assertEquals("Should preserve correct text", input, result);
    }

    @Test
    public void testSpecialSymbolsAndNoiseFiltering() throws Exception {
        // 使用反射訪問私有方法
        Method filterMethod = GoogleOCR.class.getDeclaredMethod("filterSpecialSymbolsAndNoise", String.class);
        filterMethod.setAccessible(true);

        // 測試特殊符號過濾
        String noisyText = "方式 瀏覽記錄 書籤\n" +
                          "N\n" +
                          "繁饼\n" +
                          "干杯~-b x 哗哩哔哩()口干杯~-bix] 哔哩哔哩\n" +
                          "但願來生不相逢線上看第01集\n" +
                          "G/33 B 950-9-1. html\n" +
                          "首頁\n" +
                          "電視劇\n" +
                          "W E RTY U\n" +
                          "F 2\n" +
                          "0\n" +
                          "F 3";

        String filtered = (String) filterMethod.invoke(googleOCR, noisyText);

        // 驗證有意義的內容被保留
        assertTrue("應該保留有意義的中文內容", filtered.contains("方式 瀏覽記錄 書籤"));
        assertTrue("應該保留完整的句子", filtered.contains("但願來生不相逢線上看第01集"));
        assertTrue("應該保留導航項目", filtered.contains("首頁"));
        assertTrue("應該保留分類", filtered.contains("電視劇"));

        // 驗證噪音被過濾
        assertFalse("應該過濾HTML片段", filtered.contains("G/33 B 950-9-1. html"));
        assertFalse("應該過濾無意義組合", filtered.contains("干杯~-b x"));

        // 驗證過濾後的文本更簡潔
        assertTrue("過濾後的文本應該更短", filtered.length() < noisyText.length());

        Log.d("OCRTest", "Original: " + noisyText.length() + " chars");
        Log.d("OCRTest", "Filtered: " + filtered.length() + " chars");
        Log.d("OCRTest", "Filtered text: " + filtered);
    }

    @Test
    public void testKeyboardSequenceDetection() throws Exception {
        Method containsKeyboardSequenceMethod = GoogleOCR.class.getDeclaredMethod("containsKeyboardSequence", String.class);
        containsKeyboardSequenceMethod.setAccessible(true);

        // 測試鍵盤序列檢測
        assertTrue("應該檢測到QWERTY序列",
                  (Boolean) containsKeyboardSequenceMethod.invoke(googleOCR, "qwerty"));
        assertTrue("應該檢測到ASDF序列",
                  (Boolean) containsKeyboardSequenceMethod.invoke(googleOCR, "asdf"));
        assertTrue("應該檢測到數字序列",
                  (Boolean) containsKeyboardSequenceMethod.invoke(googleOCR, "123456"));

        // 正常文本不應該被檢測為鍵盤序列
        assertFalse("正常中文不應該被檢測",
                   (Boolean) containsKeyboardSequenceMethod.invoke(googleOCR, "你好世界"));
        assertFalse("正常英文不應該被檢測",
                   (Boolean) containsKeyboardSequenceMethod.invoke(googleOCR, "Hello World"));
    }

    @Test
    public void testValidCharRatio() throws Exception {
        Method calculateValidCharRatioMethod = GoogleOCR.class.getDeclaredMethod("calculateValidCharRatio", String.class);
        calculateValidCharRatioMethod.setAccessible(true);

        // 測試有效字符比例計算
        double chineseRatio = (Double) calculateValidCharRatioMethod.invoke(googleOCR, "你好世界");
        assertTrue("純中文應該有高比例", chineseRatio > 0.9);

        double englishRatio = (Double) calculateValidCharRatioMethod.invoke(googleOCR, "Hello World");
        assertTrue("純英文應該有高比例", englishRatio > 0.9);

        double mixedRatio = (Double) calculateValidCharRatioMethod.invoke(googleOCR, "Hello 世界");
        assertTrue("混合文本應該有高比例", mixedRatio > 0.9);

        // 包含大量特殊符號的文本應該有低比例
        double symbolRatio = (Double) calculateValidCharRatioMethod.invoke(googleOCR, "↑↓←→◆◇◈◉");
        assertTrue("特殊符號多的文本應該有低比例", symbolRatio < 0.3);
    }
}
