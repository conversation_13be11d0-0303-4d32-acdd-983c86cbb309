plugins {
    // 'apply false' 表示這個插件只是在這裡聲明版本，並提供給子專案使用，
    // 而不是直接應用於根專案。
    id 'com.android.application' version '8.11.1' apply false
    id 'com.android.library' version '8.11.1' apply false
    // 新增：為所有子專案聲明 Kotlin 插件的版本
    id 'org.jetbrains.kotlin.android' version '1.9.22' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

//ext {
//    androidXVersion = '1.3.1'
//    versionCompiler = 29
//    versionTarget = 27
//    minSdkVersion = 19
//    versionCode = 126
//    versionNameString = '3.3.3'
//    javaSourceCompatibility = JavaVersion.VERSION_1_8
//    javaTargetCompatibility = JavaVersion.VERSION_1_8
////    ndkVersion = '21.0.6113669'
//    supportLibVersion = '27.1.1'
//    versionBuildTool = '27.0.3'
//    kotlinCoreVersion = '1.3.2'
//    kotlinCoroutines = '1.3.9'
//    materialVersion = '1.3.0'
//    constraintlayoutVersion = '2.0.4'
//    lifecycle_version = '2.2.0'
//    quick_version= '2.9.50'
//    dialog_version= '3.2.1'
//    bugly_version = '3.4.4'
//    bugly_native_version = '3.9.0'
//}

ext {
    androidXVersion = '1.3.1'
    // compileSdk 版本需要至少為 34 以匹配 AGP 8.2.0
    versionCompiler = 34
    // targetSdk 建議也提升到 34
    versionTarget = 34
    minSdkVersion = 21 // 考慮到現代函式庫的支援，建議提升 minSdkVersion
    versionCode = 126
    versionNameString = '3.3.3'
    // AGP 8.x 需要 Java 17
    javaSourceCompatibility = JavaVersion.VERSION_17
    javaTargetCompatibility = JavaVersion.VERSION_17
//    ndkVersion = '21.0.6113669'
    supportLibVersion = '27.1.1' // 這個是舊的 support library，在 AndroidX 中已不再使用
    versionBuildTool = '34.0.0' // Build tool 版本應與 compileSdk 對應
    kotlinCoreVersion = '1.9.0' // 建議與 Kotlin 插件版本對應
    kotlinCoroutines = '1.7.2'
    materialVersion = '1.9.0'
    constraintlayoutVersion = '2.1.4'
    lifecycle_version = '2.6.1'
    quick_version= '2.9.50'
    dialog_version= '3.2.1'
    bugly_version = '3.4.4'
    bugly_native_version = '3.9.0'
}