// 新增 pluginManagement 块，这是现代 Gradle 项目推荐的做法
pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
        // jcenter() 已被弃用，如果遇到找不到依赖的问题，可以临时加回来
        // jcenter()
    }
}
rootProject.name = "CameraSDK"
include ':app'
