# OCR 噪音過濾功能實現報告

## 問題描述

在實際使用中，OCR識別結果經常包含大量無意義的特殊符號、鍵盤序列、HTML片段等噪音內容，嚴重影響：

1. **UI顯示效果**：大量特殊符號使界面混亂
2. **TTS語音合成**：無法朗讀的符號導致語音效果差
3. **用戶體驗**：需要手動清理才能獲得有用信息

### 典型問題示例

```
方式 瀏覽記錄 書籤 設定檔 分頁 視窗 輔助說明
N
繁饼
干杯~-b x 哗哩哔哩()口干杯~-bix] 哔哩哔哩 (ˊ)口千杯~bx] 哔哩哔哩 (↑
哔哩哔哩(↑↑00千杯--bㄨ
9 但願來生不相逢線上看第01集
G/33 B 950-9-1. html
首頁
電視劇
動漫
電影
綜藝
排行 成人
1 B 禁
輸入影片關鍵字. . . . Q
白玉珠好友
王
建
国
好看 那就不贵
7月
2 G
M
N
DFl
F 2
0
F 3
2
3
$
4
F 4
%5
G
の>
DII
元
AFG
F 7
FB. F 9
F 10
F 11
&
7
W
E RTY U
B
9
0
0
P
+
F 12
```

## 解決方案

### 1. 實現架構

在 `GoogleOCR.java` 的 `correctOCRErrors()` 方法中添加新的第一步：

```java
// 0. 特殊符號和噪音過濾（新增）
corrected = filterSpecialSymbolsAndNoise(corrected);
```

### 2. 核心功能模塊

#### A. 主過濾方法 `filterSpecialSymbolsAndNoise()`

- **功能**：逐行分析文本，過濾低質量內容
- **策略**：保留有意義的行，丟棄噪音行
- **統計**：記錄過濾比例，便於調試

#### B. 行清理 `cleanLine()`

移除常見的OCR噪音符號：
- 箭頭符號：`↑↓←→⇑⇓⇐⇒` 等
- 注音符號：`ˊˇˋ˙ㄅ-ㄩ`
- 特殊圖形：`◆◇◈◉◎●○` 等
- 數學符號：`±×÷≠≤≥≈∞` 等
- 框線字符：`┌┐└┘├┤┬┴` 等

#### C. 行質量評估 `isValidLine()`

多維度評估行的有效性：
1. **長度檢查**：過短的行（<2字符）
2. **鍵盤序列檢測**：識別 QWERTY、ASDF 等
3. **HTML/URL檢測**：過濾網頁片段
4. **有效字符比例**：至少60%為有意義字符

#### D. 輔助檢測方法

1. **`containsKeyboardSequence()`**
   - 檢測常見鍵盤序列模式
   - 識別連續字母/數字序列

2. **`isHtmlOrUrlFragment()`**
   - HTML標籤檢測
   - URL和文件路徑檢測

3. **`calculateValidCharRatio()`**
   - 計算中文、英文、數字、基本標點的比例
   - 排除特殊符號和無意義字符

### 3. 過濾規則詳解

#### 保留的內容
- ✅ 有意義的中文句子：「但願來生不相逢線上看第01集」
- ✅ 導航和分類：「首頁」、「電視劇」、「動漫」
- ✅ 功能描述：「方式 瀏覽記錄 書籤」
- ✅ 正常的英文內容

#### 過濾的內容
- ❌ 鍵盤序列：「W E RTY U」、「ASDF」
- ❌ HTML片段：「G/33 B 950-9-1. html」
- ❌ 單個字符行：「N」、「M」、「G」
- ❌ 無意義組合：「干杯~-b x 哗哩哔哩()口」
- ❌ 特殊符號行：「↑↓←→◆◇◈◉」
- ❌ 功能鍵序列：「F 2」、「F 3」、「F 10」

### 4. 性能優化

1. **預編譯正則表達式**：避免重複編譯
2. **逐行處理**：減少內存使用
3. **早期退出**：快速識別明顯的噪音
4. **統計記錄**：便於性能監控

### 5. 測試覆蓋

#### 新增測試用例

1. **`testSpecialSymbolsAndNoiseFiltering()`**
   - 測試完整的噪音過濾流程
   - 驗證有意義內容的保留
   - 驗證噪音內容的過濾

2. **`testKeyboardSequenceDetection()`**
   - 測試鍵盤序列識別準確性
   - 確保正常文本不被誤判

3. **`testValidCharRatio()`**
   - 測試字符比例計算
   - 驗證不同類型文本的評分

### 6. 配置參數

| 參數 | 值 | 說明 |
|------|----|----- |
| 最小行長度 | 2字符 | 過短的行被過濾 |
| 有效字符比例閾值 | 60% | 低於此比例的行被過濾 |
| 鍵盤序列容忍度 | +3字符 | 允許序列有少量額外字符 |

### 7. 使用效果

#### 處理前
```
原始文本：包含大量噪音，共 X 行，Y 字符
```

#### 處理後
```
過濾後文本：保留有意義內容，共 A 行，B 字符
過濾比例：(X-A)/X * 100%
```

### 8. 日誌輸出

```
D/GoogleOCR: Noise filtering: 8/25 lines kept (32.0%)
```

提供過濾統計，便於：
- 調試過濾效果
- 調整過濾參數
- 監控處理性能

## 總結

### ✅ 實現的功能

1. **智能噪音檢測**：多維度評估內容質量
2. **保守過濾策略**：優先保留有意義內容
3. **高效處理**：逐行處理，性能優化
4. **全面測試**：覆蓋各種噪音場景
5. **詳細日誌**：便於調試和監控

### 🎯 預期效果

1. **UI顯示**：更清潔、更易讀
2. **TTS效果**：更流暢、更自然
3. **用戶體驗**：減少手動清理工作
4. **處理效率**：自動化噪音過濾

### 🔧 後續優化方向

1. **機器學習**：基於用戶反饋優化過濾規則
2. **自適應閾值**：根據文本類型調整參數
3. **語言特定**：針對不同語言優化規則
4. **用戶配置**：允許用戶自定義過濾強度
