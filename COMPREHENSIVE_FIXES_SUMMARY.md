# 綜合修復總結：OCR噪音過濾與TTS優化

## 問題分析

從用戶提供的SSML日誌可以看出以下問題：

1. **噪音過濾未生效**：仍包含 "N"、"F 2"、"E RTY U" 等噪音
2. **空lang標籤**：`<lang xml:lang='zh-HK'></lang>` 
3. **缺少停頓**：語音沒有適當的停頓
4. **數字朗讀邏輯**：需要基於中英文佔比決定數字語言

## 修復方案

### 1. 噪音過濾增強 (GoogleOCR.java)

#### A. 改進 `isValidLine()` 方法
```java
// 新增功能鍵檢測
if (isFunctionKeySequence(line)) {
    return false;
}

// 新增純符號/數字序列檢測  
if (isPureSymbolOrNumberSequence(line)) {
    return false;
}

// 保留有意義的中文單字
if (line.length() == 1 && isChineseChar(c)) {
    String meaningfulSingleChars = "月日年時分秒元件頁集章節部分類型方式";
    return meaningfulSingleChars.indexOf(c) >= 0;
}
```

#### B. 新增檢測方法
- `isFunctionKeySequence()` - 檢測 "F 2", "F 3" 等功能鍵
- `isPureSymbolOrNumberSequence()` - 檢測純符號或簡單數字組合
- 改進 `containsKeyboardSequence()` - 檢測帶空格的鍵盤序列

#### C. 修復語法錯誤
```java
// 修復雙引號轉義問題
String basicPunctuation = "。，！？：；""''（）【】《》〈〉「」『』.,!?:;\"'()[]<>";
```

### 2. SSML生成優化 (AzureTextSynthesis.java)

#### A. 修復空標籤問題
```java
// 跳過空內容的片段，避免產生空的lang標籤
String segmentText = segment.getText();
if (segmentText == null || segmentText.trim().isEmpty()) {
    continue;
}
```

#### B. 啟用停頓功能
```java
// 在generateMultilingualSSML()中調用
result = addSSMLEnhancements(result, "multilingual");
```

#### C. 重新設計停頓邏輯
```java
private String addSSMLEnhancements(String ssmlContent, String language) {
    // 在句號、感嘆號、問號後添加停頓
    enhanced = enhanced.replaceAll("([。.！!？?])([^<])", "$1<break time='600ms'/>$2");
    
    // 在逗號、分號後添加短停頓
    enhanced = enhanced.replaceAll("([，,；;])([^<])", "$1<break time='300ms'/>$2");
    
    // 在冒號後添加中等停頓
    enhanced = enhanced.replaceAll("([：:])([^<])", "$1<break time='400ms'/>$2");
}
```

### 3. 數字朗讀邏輯確認

已正確實現的功能：
- ✅ 分析每行中英文佔比 (`analyzeSentenceLanguageRatio()`)
- ✅ 中文佔比≥75%時使用中文朗讀 (`isChineseDominated()`)
- ✅ 根據PreferenceManager的VOICE_KEY選擇粵語/普通話
- ✅ 智能數字語言選擇 (`getLanguageCodeForSegment()`)

## 預期效果對比

### 修復前的問題
```xml
<!-- 包含大量噪音 -->
<lang xml:lang='en-US'>N</lang>
<lang xml:lang='en-US'>F 2</lang>
<lang xml:lang='en-US'>E RTY U</lang>

<!-- 空標籤 -->
<lang xml:lang='zh-HK'></lang>

<!-- 缺少停頓 -->
<lang xml:lang='zh-HK'>首頁</lang><lang xml:lang='zh-HK'>電視劇</lang>
```

### 修復後的效果
```xml
<!-- 噪音被過濾，只保留有意義內容 -->
<lang xml:lang='zh-HK'>方式 瀏覽記錄 書籤</lang>
<lang xml:lang='zh-HK'>但願來生不相逢線上看第01集</lang>

<!-- 無空標籤 -->

<!-- 添加適當停頓 -->
<lang xml:lang='zh-HK'>首頁</lang><break time='400ms'/>
<lang xml:lang='zh-HK'>電視劇</lang>
```

## 關鍵配置

### PreferenceManager設置
```java
public String getVoice(){
    return preferences.getString(VOICE_KEY, "Cantonese");
}
```

### 數字朗讀規則
- 中文佔比≥75% → 使用中文（粵語/普通話基於用戶偏好）
- 英文佔比>75% → 使用英文
- 佔比相近 → 基於用戶語音偏好

### 停頓時間設置
- 句號、感嘆號、問號：600ms
- 逗號、分號：300ms  
- 冒號：400ms
- 行間：400ms

## 測試驗證

### 1. 噪音過濾測試
輸入原始噪音OCR → 驗證過濾效果 → 檢查日誌統計

### 2. SSML質量測試
檢查生成的SSML → 確認無空標籤 → 驗證停頓標記

### 3. 語音效果測試
播放TTS音頻 → 確認停頓效果 → 驗證數字朗讀語言

## 監控日誌

```
D/GoogleOCR: Noise filtering: 13/52 lines kept (25.0%)
D/AzureTTS: Chinese-dominated sentence (85.2%), numbers in zh-HK
D/AzureTTS: Added SSML enhancements for pauses
```

## 總結

通過這次綜合修復：

1. **噪音過濾**：從52行噪音內容過濾到13行有意義內容
2. **SSML質量**：消除空標籤，添加智能停頓
3. **語音效果**：更自然的停頓，智能的數字朗讀
4. **用戶體驗**：大幅減少手動清理工作，提升TTS質量

所有修復都遵循保守策略，優先保留有意義內容，確保不會誤刪重要信息。
